#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل دليل سفر العراقيين إلى الإمارات 2024 إلى صور
Convert UAE Travel Guide to Images
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from docx import Document
import textwrap
import re

class TravelGuideConverter:
    def __init__(self):
        self.width = 1080
        self.height = 1920  # 9:16 aspect ratio
        self.margin = 80
        self.line_spacing = 45
        self.title_font_size = 42
        self.heading_font_size = 36
        self.body_font_size = 28
        self.small_font_size = 24
        
        # Colors
        self.bg_color = (255, 255, 255)  # White
        self.title_color = (0, 51, 102)  # Dark blue
        self.heading_color = (0, 102, 204)  # Medium blue
        self.text_color = (51, 51, 51)  # Dark gray
        self.accent_color = (255, 193, 7)  # Gold
        
        # Load fonts
        self.fonts = self.load_fonts()
        
    def load_fonts(self):
        """Load Arabic fonts"""
        fonts = {}
        
        # Font paths
        font_paths = [
            "C:/Windows/Fonts/",
            "./fonts/",
            "/System/Library/Fonts/",
            "/usr/share/fonts/"
        ]
        
        # Font files
        font_files = {
            'amiri': ['Amiri-Regular.ttf', 'amiri-regular.ttf'],
            'arabic': ['arabtype.ttf', 'Arabic Typesetting Regular.ttf'],
            'tahoma': ['tahoma.ttf', 'Tahoma.ttf']
        }
        
        # Search for fonts
        for font_name, filenames in font_files.items():
            for path in font_paths:
                for filename in filenames:
                    font_path = os.path.join(path, filename)
                    if os.path.exists(font_path):
                        try:
                            fonts[font_name] = font_path
                            print(f"✓ Found font {font_name}: {font_path}")
                            break
                        except:
                            continue
                if font_name in fonts:
                    break
        
        if not fonts:
            print("⚠️ No Arabic fonts found, using default")
            fonts['default'] = None
            
        return fonts
    
    def get_font(self, size, bold=False):
        """Get font with specified size"""
        try:
            if 'amiri' in self.fonts:
                return ImageFont.truetype(self.fonts['amiri'], size)
            elif 'arabic' in self.fonts:
                return ImageFont.truetype(self.fonts['arabic'], size)
            elif 'tahoma' in self.fonts:
                return ImageFont.truetype(self.fonts['tahoma'], size)
            else:
                return ImageFont.load_default()
        except:
            return ImageFont.load_default()
    
    def wrap_text(self, text, font, max_width):
        """Wrap Arabic text to lines"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = f"{current_line} {word}".strip()
            bbox = font.getbbox(test_line)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def draw_header(self, draw, y_pos):
        """Draw page header"""
        header_height = 120
        draw.rectangle([0, 0, self.width, header_height], fill=self.title_color)
        
        title = "دليل سفر العراقيين إلى الإمارات 2024"
        title_font = self.get_font(self.title_font_size, bold=True)
        
        bbox = title_font.getbbox(title)
        text_width = bbox[2] - bbox[0]
        x_pos = (self.width - text_width) // 2
        
        draw.text((x_pos, 30), title, font=title_font, fill=(255, 255, 255))
        
        return header_height + 40
    
    def draw_footer(self, draw, page_num, total_pages):
        """Draw page footer"""
        footer_y = self.height - 80
        
        page_text = f"صفحة {page_num} من {total_pages}"
        footer_font = self.get_font(self.small_font_size)
        
        bbox = footer_font.getbbox(page_text)
        text_width = bbox[2] - bbox[0]
        x_pos = (self.width - text_width) // 2
        
        draw.text((x_pos, footer_y), page_text, font=footer_font, fill=self.text_color)
        
        # Separator line
        draw.line([self.margin, footer_y - 20, self.width - self.margin, footer_y - 20], 
                 fill=self.accent_color, width=2)
    
    def process_paragraph(self, paragraph):
        """Process document paragraph"""
        text = paragraph.text.strip()
        if not text:
            return None, None
        
        # Determine text type
        if any(keyword in text for keyword in ['شروط', 'أنواع', 'الوثائق', 'خطوات']):
            return 'heading', text
        elif text.startswith('✅') or text.startswith('⚠️') or text.startswith('📋'):
            return 'bullet', text
        elif len(text) < 100 and any(keyword in text for keyword in ['تأشيرة', 'إقامة', 'سفر']):
            return 'title', text
        else:
            return 'body', text
    
    def draw_text_block(self, draw, text, text_type, y_pos, max_width):
        """Draw text block"""
        if text_type == 'title':
            font = self.get_font(self.heading_font_size, bold=True)
            color = self.title_color
        elif text_type == 'heading':
            font = self.get_font(self.heading_font_size)
            color = self.heading_color
        else:
            font = self.get_font(self.body_font_size)
            color = self.text_color
        
        # Wrap text to lines
        lines = self.wrap_text(text, font, max_width)
        
        current_y = y_pos
        for line in lines:
            # Right align for Arabic
            bbox = font.getbbox(line)
            text_width = bbox[2] - bbox[0]
            x_pos = self.width - self.margin - text_width
            
            draw.text((x_pos, current_y), line, font=font, fill=color)
            current_y += self.line_spacing
        
        return current_y + 20
    
    def convert_document(self, docx_path, output_dir):
        """Convert document to images"""
        print(f"🔄 Converting file: {docx_path}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Read document
        try:
            doc = Document(docx_path)
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return []
        
        # Process paragraphs
        paragraphs = []
        for para in doc.paragraphs:
            result = self.process_paragraph(para)
            if result[0]:
                paragraphs.append(result)
        
        # Split content to pages
        pages = self.split_to_pages(paragraphs)
        
        # Create images
        image_paths = []
        total_pages = len(pages)
        
        for page_num, page_content in enumerate(pages, 1):
            print(f"📄 Creating page {page_num} of {total_pages}")
            
            # Create new image
            img = Image.new('RGB', (self.width, self.height), self.bg_color)
            draw = ImageDraw.Draw(img)
            
            # Draw header
            current_y = self.draw_header(draw, 0)
            
            # Draw content
            max_width = self.width - (2 * self.margin)
            
            for text_type, text in page_content:
                if current_y < self.height - 150:
                    current_y = self.draw_text_block(draw, text, text_type, current_y, max_width)
            
            # Draw footer
            self.draw_footer(draw, page_num, total_pages)
            
            # Save image
            filename = f"travel_guide_page_{page_num:02d}.png"
            filepath = os.path.join(output_dir, filename)
            img.save(filepath, 'PNG', quality=95, optimize=True)
            image_paths.append(filepath)
            
            print(f"✅ Saved: {filename}")
        
        return image_paths
    
    def split_to_pages(self, paragraphs):
        """Split content to pages"""
        pages = []
        current_page = []
        current_height = 160  # Header height
        max_height = self.height - 200  # Leave space for footer
        
        for text_type, text in paragraphs:
            # Estimate text height
            if text_type == 'title':
                estimated_height = 80
            elif text_type == 'heading':
                estimated_height = 60
            else:
                estimated_lines = len(text) // 40 + 1
                estimated_height = estimated_lines * self.line_spacing + 20
            
            # Check if text fits in current page
            if current_height + estimated_height > max_height and current_page:
                pages.append(current_page)
                current_page = [(text_type, text)]
                current_height = 160 + estimated_height
            else:
                current_page.append((text_type, text))
                current_height += estimated_height
        
        # Add last page
        if current_page:
            pages.append(current_page)
        
        return pages

def main():
    """Main function"""
    print("🎨 UAE Travel Guide to Images Converter")
    print("=" * 50)
    
    # File paths
    docx_file = "دليل_سفر_العراقيين_الى_الامارات_2024.docx"
    output_directory = "travel_guide_images"
    
    # Check if file exists
    if not os.path.exists(docx_file):
        print(f"❌ File not found: {docx_file}")
        return
    
    # Create converter
    converter = TravelGuideConverter()
    
    # Convert document
    try:
        image_paths = converter.convert_document(docx_file, output_directory)
        
        if image_paths:
            print(f"\n🎉 Conversion successful!")
            print(f"📁 Directory: {output_directory}")
            print(f"📊 Number of images: {len(image_paths)}")
            print(f"📐 Dimensions: {converter.width}x{converter.height} (9:16)")
            
            # Calculate total size
            total_size = sum(os.path.getsize(p) for p in image_paths if os.path.exists(p))
            print(f"💾 Total size: {total_size / 1024 / 1024:.1f} MB")
            
            print("\n📋 Generated images:")
            for i, path in enumerate(image_paths, 1):
                filename = os.path.basename(path)
                if os.path.exists(path):
                    size_kb = os.path.getsize(path) / 1024
                    print(f"   {i:2d}. {filename} ({size_kb:.1f} KB)")
                
        else:
            print("❌ Failed to create images")
            
    except Exception as e:
        print(f"❌ Conversion error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
