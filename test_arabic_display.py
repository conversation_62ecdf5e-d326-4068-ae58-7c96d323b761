#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض النص العربي بشكل صحيح
Test Arabic text display correctly
"""

from PIL import Image, ImageDraw, ImageFont
from bidi.algorithm import get_display
import arabic_reshaper
import os

def process_arabic_text(text):
    """معالجة النص العربي للعرض الصحيح من اليمين إلى اليسار"""
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية الاتجاه الثنائي
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في معالجة النص: {e}")
        return text

def create_test_image():
    """إنشاء صورة اختبار للنص العربي"""
    
    # إنشاء صورة بيضاء
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # محاولة تحميل خط عربي
    try:
        font_large = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 40)
        font_medium = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 30)
        print("تم تحميل الخط بنجاح")
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        print("تم استخدام الخط الافتراضي")
    
    # نصوص للاختبار
    test_texts = [
        "جفاف مياه دجلة والفرات في العراق",
        "بلاد الرافدين: مهد الحضارة",
        "الوضع الحالي: أزمة مياه حادة",
        "الأسباب الخارجية للأزمة",
        "السدود التركية والإيرانية"
    ]
    
    y_position = 50
    
    # رسم النصوص
    for i, text in enumerate(test_texts):
        # النص الأصلي (بدون معالجة)
        draw.text((50, y_position), f"أصلي: {text}", fill='red', font=font_medium)
        
        # النص المعالج
        processed_text = process_arabic_text(text)
        draw.text((50, y_position + 40), f"معالج: {processed_text}", fill='blue', font=font_medium)
        
        y_position += 100
    
    # حفظ الصورة
    img.save("اختبار_النص_العربي.png")
    print("تم حفظ صورة الاختبار: اختبار_النص_العربي.png")

def display_text_comparison():
    """عرض مقارنة النصوص"""
    
    print("=" * 60)
    print("اختبار عرض النص العربي")
    print("Arabic Text Display Test")
    print("=" * 60)
    
    test_texts = [
        "جفاف مياه دجلة والفرات في العراق",
        "بلاد الرافدين: مهد الحضارة", 
        "الوضع الحالي: أزمة مياه حادة",
        "الأسباب الخارجية للأزمة",
        "السدود التركية والإيرانية",
        "التغير المناخي وقلة الأمطار",
        "تراجع المساحات المزروعة",
        "نزوح السكان من المناطق المتضررة",
        "الحلول والمقترحات",
        "إنقاذ بلاد الرافدين"
    ]
    
    print("\n📝 النصوص الأصلية:")
    print("-" * 40)
    for i, text in enumerate(test_texts, 1):
        print(f"{i:2d}. {text}")
    
    print("\n🔄 النصوص المعالجة (RTL):")
    print("-" * 40)
    for i, text in enumerate(test_texts, 1):
        processed = process_arabic_text(text)
        print(f"{i:2d}. {processed}")
    
    print("\n" + "=" * 60)
    print("ملاحظة: النصوص المعالجة يجب أن تظهر بشكل صحيح")
    print("في التطبيقات التي تدعم النص العربي")
    print("=" * 60)

if __name__ == "__main__":
    display_text_comparison()
    create_test_image()
