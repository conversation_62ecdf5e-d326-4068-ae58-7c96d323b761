#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Run Script for AI Conversation Video Generator
Simple interface to create AI-style conversation videos
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("🔧 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_ai_video.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages. Please install manually:")
        print("pip install moviepy pillow pyttsx3 numpy arabic-reshaper python-bidi")
        return False

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['moviepy', 'PIL', 'pyttsx3', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def main():
    """Main function"""
    print("🤖 AI Conversation Video Generator")
    print("=" * 50)
    
    # Check requirements
    requirements_ok, missing = check_requirements()
    
    if not requirements_ok:
        print(f"⚠️ Missing packages: {', '.join(missing)}")
        install_choice = input("Do you want to install them automatically? (y/n): ")
        
        if install_choice.lower() == 'y':
            if not install_requirements():
                return
        else:
            print("Please install the required packages and try again.")
            return
    
    # Import and run the video generator
    try:
        from ai_conversation_video import AIConversationVideo
        
        print("\n🎬 Creating your AI conversation video...")
        generator = AIConversationVideo()
        video_file = generator.create_video()
        
        print(f"\n🎉 Success! Your video is ready: {video_file}")
        
        # Ask if user wants to open the video
        open_choice = input("\nDo you want to open the video now? (y/n): ")
        if open_choice.lower() == 'y':
            if os.name == 'nt':  # Windows
                os.startfile(video_file)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open' if sys.platform == 'darwin' else 'xdg-open', video_file])
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("\nPlease make sure all files are in the same directory:")
        print("• ai_conversation_video.py")
        print("• requirements_ai_video.txt")
        print("• run_ai_video.py")

if __name__ == "__main__":
    main()
