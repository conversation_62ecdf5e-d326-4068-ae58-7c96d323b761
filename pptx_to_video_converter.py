#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل عرض البوربوينت إلى فيديو
Convert PowerPoint presentation to video
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import moviepy.editor as mp
from moviepy.editor import ImageSequence<PERSON>lip, AudioFileClip
import tempfile
import shutil
from bidi.algorithm import get_display
import arabic_reshaper

def process_arabic_text(text):
    """معالجة النص العربي للعرض الصحيح من اليمين إلى اليسار"""
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية الاتجاه الثنائي
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except:
        # في حالة فشل المعالجة، إرجاع النص الأصلي
        return text

def convert_pptx_to_images(pptx_file):
    """تحويل شرائح البوربوينت إلى صور"""
    try:
        # استخدام python-pptx لقراءة العرض التقديمي
        from pptx import Presentation

        prs = Presentation(pptx_file)
        images = []
        temp_dir = tempfile.mkdtemp()

        print(f"تم العثور على {len(prs.slides)} شريحة في العرض التقديمي")

        # بيانات الشرائح المفصلة
        slides_data = [
            {
                "title": "جفاف مياه دجلة والفرات في العراق",
                "subtitle": "أزمة المياه في بلاد الرافدين",
                "bg_color": (0, 51, 102),
                "text_color": (255, 255, 255),
                "subtitle_color": (255, 255, 255)
            },
            {
                "title": "بلاد الرافدين: مهد الحضارة",
                "content": [
                    "• نهرا دجلة والفرات: شريان الحياة في العراق منذ آلاف السنين",
                    "• مهد الحضارات القديمة: بابل وآشور وسومر",
                    "• مصدر المياه الرئيسي للزراعة والشرب والصناعة",
                    "• يغذي النهران أكثر من 40 مليون نسمة في المنطقة"
                ],
                "bg_color": (245, 245, 245),
                "text_color": (0, 51, 102),
                "content_color": (51, 51, 51)
            },
            {
                "title": "الوضع الحالي: أزمة مياه حادة",
                "content": [
                    "• انخفاض منسوب المياه بنسبة 29% في دجلة و73% في الفرات",
                    "• تحول أجزاء من النهرين إلى برك من مياه الصرف الصحي",
                    "• تهديد بجفاف كامل للنهرين بحلول عام 2040",
                    "• تأثر 7 ملايين عراقي بنقص المياه الصالحة للشرب"
                ],
                "bg_color": (255, 240, 240),
                "text_color": (139, 0, 0),
                "content_color": (139, 0, 0)
            },
            {
                "title": "الأسباب الخارجية للأزمة",
                "content": [
                    "• السدود التركية: 22 سداً على نهري دجلة والفرات",
                    "• سد أليسو التركي: يحجب 50% من مياه دجلة",
                    "• السدود الإيرانية: تحويل مجرى روافد دجلة",
                    "• عدم التزام الدول المجاورة بالاتفاقيات المائية الدولية"
                ],
                "bg_color": (240, 248, 255),
                "text_color": (0, 51, 102),
                "content_color": (51, 51, 51)
            },
            {
                "title": "الأسباب الداخلية والمناخية",
                "content": [
                    "• التغير المناخي: ارتفاع درجات الحرارة وقلة الأمطار",
                    "• موسم 2020-2021: ثاني أكثر المواسم جفافاً منذ 40 عاماً",
                    "• سوء إدارة الموارد المائية المحلية",
                    "• تلوث المياه بسبب مياه الصرف الصحي والنفايات الصناعية"
                ],
                "bg_color": (255, 248, 220),
                "text_color": (184, 134, 11),
                "content_color": (139, 69, 19)
            },
            {
                "title": "التأثير على القطاع الزراعي",
                "content": [
                    "• تراجع المساحات المزروعة بنسبة 70%",
                    "• انخفاض إنتاج القمح والشعير والأرز",
                    "• هجرة المزارعين من الريف إلى المدن",
                    "• فقدان العراق لاكتفائه الذاتي من المحاصيل الأساسية"
                ],
                "bg_color": (240, 255, 240),
                "text_color": (34, 139, 34),
                "content_color": (0, 100, 0)
            },
            {
                "title": "التأثيرات الاجتماعية والاقتصادية",
                "content": [
                    "• نزوح أكثر من 400 ألف شخص من المناطق المتضررة",
                    "• ارتفاع معدلات البطالة في القطاع الزراعي",
                    "• تدهور الأوضاع الصحية بسبب تلوث المياه",
                    "• تهديد الأمن الغذائي والاستقرار الاجتماعي"
                ],
                "bg_color": (255, 245, 238),
                "text_color": (205, 92, 92),
                "content_color": (139, 69, 19)
            },
            {
                "title": "تأثير على قطاع الكهرباء",
                "content": [
                    "• انخفاض إنتاج الكهرباء من السدود المائية",
                    "• توقف عدة محطات توليد كهرومائية عن العمل",
                    "• زيادة الاعتماد على الوقود الأحفوري المكلف",
                    "• تأثير سلبي على الصناعات التي تعتمد على الكهرباء"
                ],
                "bg_color": (255, 255, 224),
                "text_color": (255, 140, 0),
                "content_color": (255, 69, 0)
            },
            {
                "title": "الحلول والمقترحات",
                "content": [
                    "• التفاوض مع تركيا وإيران لضمان حصة عادلة من المياه",
                    "• تطبيق الاتفاقيات المائية الدولية",
                    "• تحسين كفاءة استخدام المياه في الزراعة",
                    "• معالجة مياه الصرف الصحي وإعادة استخدامها"
                ],
                "bg_color": (240, 255, 240),
                "text_color": (34, 139, 34),
                "content_color": (0, 100, 0)
            },
            {
                "title": "الحلول التقنية والبديلة",
                "content": [
                    "• تحلية مياه البحر من الخليج العربي",
                    "• حفر آبار جوفية عميقة بتقنيات حديثة",
                    "• بناء سدود وخزانات مائية محلية",
                    "• تطوير مصادر طاقة متجددة لتشغيل محطات التحلية"
                ],
                "bg_color": (230, 244, 255),
                "text_color": (0, 51, 102),
                "content_color": (25, 25, 112)
            },
            {
                "title": "دور المجتمع الدولي",
                "content": [
                    "• الضغط الدولي على تركيا وإيران لاحترام حقوق المياه",
                    "• تقديم المساعدات التقنية والمالية للعراق",
                    "• دعم مشاريع البنية التحتية المائية",
                    "• التعاون في مواجهة تحديات التغير المناخي"
                ],
                "bg_color": (248, 248, 255),
                "text_color": (75, 0, 130),
                "content_color": (72, 61, 139)
            },
            {
                "title": "الخاتمة: إنقاذ بلاد الرافدين",
                "content": [
                    "أزمة جفاف دجلة والفرات تهدد مستقبل العراق وحضارته العريقة",
                    "",
                    "الحل يتطلب تضافر الجهود المحلية والإقليمية والدولية",
                    "",
                    "المياه حق إنساني أساسي وليس سلاحاً سياسياً"
                ],
                "bg_color": (255, 248, 220),
                "text_color": (184, 134, 11),
                "content_color": (139, 69, 19)
            }
        ]

        # إنشاء صور للشرائح
        for i, slide_data in enumerate(slides_data):
            if i >= len(prs.slides):
                break

            # إنشاء صورة فارغة بحجم 1920x1080
            img = Image.new('RGB', (1920, 1080), color=slide_data["bg_color"])
            draw = ImageDraw.Draw(img)

            # محاولة تحميل خط عربي
            try:
                # محاولة استخدام خطوط عربية
                font_paths = [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    "arial.ttf",
                    "tahoma.ttf"
                ]

                font_title = None
                font_content = None
                font_subtitle = None

                for font_path in font_paths:
                    try:
                        font_title = ImageFont.truetype(font_path, 60)
                        font_content = ImageFont.truetype(font_path, 32)
                        font_subtitle = ImageFont.truetype(font_path, 40)
                        break
                    except:
                        continue

                if font_title is None:
                    font_title = ImageFont.load_default()
                    font_content = ImageFont.load_default()
                    font_subtitle = ImageFont.load_default()

            except:
                font_title = ImageFont.load_default()
                font_content = ImageFont.load_default()
                font_subtitle = ImageFont.load_default()

            # رسم العنوان مع معالجة النص العربي
            title = process_arabic_text(slide_data["title"])
            bbox = draw.textbbox((0, 0), title, font=font_title)
            text_width = bbox[2] - bbox[0]
            x = (1920 - text_width) // 2
            y = 100

            draw.text((x, y), title, fill=slide_data["text_color"], font=font_title)

            # رسم العنوان الفرعي أو المحتوى
            if "subtitle" in slide_data:
                subtitle = process_arabic_text(slide_data["subtitle"])
                bbox_sub = draw.textbbox((0, 0), subtitle, font=font_subtitle)
                sub_width = bbox_sub[2] - bbox_sub[0]
                x_sub = (1920 - sub_width) // 2
                y_sub = 300
                draw.text((x_sub, y_sub), subtitle, fill=slide_data["subtitle_color"], font=font_subtitle)

            elif "content" in slide_data:
                content_lines = slide_data["content"]
                y_start = 250
                line_height = 60

                for j, line in enumerate(content_lines):
                    if line.strip():  # تجاهل الأسطر الفارغة
                        processed_line = process_arabic_text(line)
                        y_pos = y_start + (j * line_height)

                        # محاذاة النص من اليمين للعربي
                        bbox_line = draw.textbbox((0, 0), processed_line, font=font_content)
                        line_width = bbox_line[2] - bbox_line[0]
                        x_pos = 1820 - line_width  # محاذاة من اليمين مع هامش

                        draw.text((x_pos, y_pos), processed_line, fill=slide_data["content_color"], font=font_content)

            # حفظ الصورة
            img_path = os.path.join(temp_dir, f"slide_{i+1:02d}.png")
            img.save(img_path)
            images.append(img_path)
            print(f"تم إنشاء الشريحة {i+1}: {slide_data['title']}")

        return images, temp_dir

    except Exception as e:
        print(f"خطأ في تحويل العرض التقديمي: {e}")
        return [], None

def create_video_from_images(images, output_file, duration_per_slide=5):
    """إنشاء فيديو من الصور"""
    try:
        if not images:
            print("لا توجد صور لإنشاء الفيديو")
            return False

        print(f"إنشاء فيديو من {len(images)} صورة...")

        # إنشاء مقاطع فيديو من الصور
        clips = []
        for img_path in images:
            # إنشاء مقطع فيديو من الصورة لمدة محددة
            clip = mp.ImageClip(img_path, duration=duration_per_slide)
            clips.append(clip)

        # دمج جميع المقاطع
        final_video = mp.concatenate_videoclips(clips, method="compose")

        # تعيين معدل الإطارات
        final_video = final_video.set_fps(30)

        # كتابة الفيديو
        final_video.write_videofile(
            output_file,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )

        print(f"تم إنشاء الفيديو بنجاح: {output_file}")
        return True

    except Exception as e:
        print(f"خطأ في إنشاء الفيديو: {e}")
        return False

def convert_presentation_to_video(pptx_file, output_video="جفاف_دجلة_والفرات_فيديو.mp4", duration_per_slide=5):
    """تحويل العرض التقديمي إلى فيديو"""

    print("بدء تحويل العرض التقديمي إلى فيديو...")

    # تحويل العرض التقديمي إلى صور
    images, temp_dir = convert_pptx_to_images(pptx_file)

    if not images:
        print("فشل في تحويل العرض التقديمي إلى صور")
        return False

    # إنشاء الفيديو من الصور
    success = create_video_from_images(images, output_video, duration_per_slide)

    # تنظيف الملفات المؤقتة
    if temp_dir and os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
        print("تم حذف الملفات المؤقتة")

    return success

if __name__ == "__main__":
    # تشغيل التحويل
    pptx_file = "جفاف_دجلة_والفرات_في_العراق.pptx"

    if os.path.exists(pptx_file):
        print(f"تحويل الملف: {pptx_file}")
        success = convert_presentation_to_video(pptx_file)

        if success:
            print("تم التحويل بنجاح!")
        else:
            print("فشل في التحويل")
    else:
        print(f"الملف غير موجود: {pptx_file}")
        print("يرجى تشغيل سكريبت إنشاء العرض التقديمي أولاً")
