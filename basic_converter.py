from PIL import Image, ImageDraw, ImageFont
import os

def create_travel_guide_images():
    # Sample content from UAE travel guide
    content = [
        "دليل سفر العراقيين إلى الإمارات 2024",
        "",
        "شروط السفر والتأشيرة:",
        "• تأشيرة سياحية: 30 يوم - 100 درهم",
        "• تأشيرة ترانزيت: 96 ساعة - 50 درهم", 
        "• تأشيرة متعددة: 90 يوم - 300 درهم",
        "",
        "الوثائق المطلوبة:",
        "• جواز سفر عراقي صالح لمدة 6 أشهر",
        "• صورة شخصية خلفية بيضاء",
        "• حجز فندق مؤكد",
        "• تذكرة طيران ذهاب وإياب",
        "• كشف حساب بنكي آخر 3 أشهر",
        "",
        "أنواع الإقامة:",
        "• إقامة عمل: 2-3 سنوات",
        "• إقامة استثمار: 2-3 سنوات",
        "• الإقامة الذهبية: 5-10 سنوات",
        "• إقامة طالب: سنة دراسية",
        "• إقامة تقاعد: 5 سنوات",
        "",
        "خطوات التقديم:",
        "1. تجهيز الوثائق المطلوبة",
        "2. تقديم طلب التأشيرة",
        "3. دفع الرسوم المطلوبة",
        "4. انتظار الموافقة (3-7 أيام)",
        "5. طباعة التأشيرة والسفر",
        "",
        "معلومات مهمة:",
        "• القنصلية العراقية في دبي: +971-4-3971717",
        "• السفارة العراقية في أبوظبي: +971-2-4446633",
        "• الهجرة والجوازات: +971-4-3131111",
        "",
        "نصائح للسفر:",
        "• احتفظ بنسخة من جميع الوثائق",
        "• تأكد من صحة المعلومات قبل السفر",
        "• راجع المواقع الرسمية للتحديثات",
        "• احجز الفندق مسبقاً",
        "• تأكد من صلاحية جواز السفر"
    ]
    
    # Settings
    width = 1080
    height = 1920
    margin = 60
    line_height = 50
    
    # Colors
    bg_color = (255, 255, 255)
    title_color = (0, 51, 102)
    text_color = (51, 51, 51)
    accent_color = (255, 193, 7)
    
    # Create output directory
    output_dir = "travel_guide_images"
    os.makedirs(output_dir, exist_ok=True)
    
    # Split content into pages
    lines_per_page = 30
    pages = []
    current_page = []
    
    for line in content:
        current_page.append(line)
        if len(current_page) >= lines_per_page:
            pages.append(current_page)
            current_page = []
    
    if current_page:
        pages.append(current_page)
    
    print(f"Creating {len(pages)} pages...")
    
    # Create images
    for page_num, page_content in enumerate(pages, 1):
        print(f"Creating page {page_num}...")
        
        # Create image
        img = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(img)
        
        # Try to load font
        try:
            font = ImageFont.truetype("arial.ttf", 28)
            title_font = ImageFont.truetype("arial.ttf", 36)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 28)
                title_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 36)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
        
        # Draw header background
        draw.rectangle([0, 0, width, 120], fill=title_color)
        
        # Draw title
        title = "UAE Travel Guide 2024"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        draw.text((title_x, 40), title, font=title_font, fill=(255, 255, 255))
        
        # Draw content
        y_pos = 160
        
        for line in page_content:
            if y_pos > height - 150:
                break
                
            if line.strip() == "":
                y_pos += line_height // 2
                continue
                
            # Determine text color and font
            if line.startswith("دليل"):
                current_font = title_font
                color = title_color
            elif line.endswith(":"):
                current_font = title_font
                color = title_color
            else:
                current_font = font
                color = text_color
            
            # Draw text
            draw.text((margin, y_pos), line, font=current_font, fill=color)
            y_pos += line_height
        
        # Draw footer
        footer_text = f"Page {page_num} of {len(pages)}"
        footer_bbox = draw.textbbox((0, 0), footer_text, font=font)
        footer_width = footer_bbox[2] - footer_bbox[0]
        footer_x = (width - footer_width) // 2
        
        # Draw separator line
        draw.line([margin, height - 100, width - margin, height - 100], 
                 fill=accent_color, width=3)
        
        draw.text((footer_x, height - 80), footer_text, font=font, fill=text_color)
        
        # Save image
        filename = f"travel_guide_page_{page_num:02d}.png"
        filepath = os.path.join(output_dir, filename)
        img.save(filepath, 'PNG', quality=95)
        
        print(f"Saved: {filename}")
    
    print(f"\nConversion complete!")
    print(f"Created {len(pages)} images in '{output_dir}' directory")
    
    # List created files
    for i in range(1, len(pages) + 1):
        filename = f"travel_guide_page_{i:02d}.png"
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            size_kb = os.path.getsize(filepath) / 1024
            print(f"  {i}. {filename} ({size_kb:.1f} KB)")

if __name__ == "__main__":
    create_travel_guide_images()
