# مشروع جفاف مياه دجلة والفرات في العراق
## Tigris and Euphrates Drought in Iraq Project

### نظرة عامة / Overview

هذا المشروع يتضمن إنشاء عرض تقديمي شامل وفيديو تعليمي حول أزمة جفاف مياه نهري دجلة والفرات في العراق، وهي واحدة من أخطر التحديات البيئية والاقتصادية التي تواجه العراق في العصر الحديث.

This project includes creating a comprehensive presentation and educational video about the drought crisis of the Tigris and Euphrates rivers in Iraq, one of the most serious environmental and economic challenges facing Iraq in modern times.

### محتويات المشروع / Project Contents

#### 1. العرض التقديمي / PowerPoint Presentation
- **الملف**: `جفاف_دجلة_والفرات_في_العراق.pptx`
- **عدد الشرائح**: 12 شريحة
- **التنسيق**: 16:9 (1920x1080)
- **الخط المستخدم**: Amiri (خط عربي عالي الجودة)

#### 2. الفيديو التعليمي / Educational Video
- **الملف**: `جفاف_دجلة_والفرات_فيديو.mp4`
- **المدة**: 60 ثانية (5 ثوانٍ لكل شريحة)
- **الدقة**: 1920x1080 (Full HD)
- **معدل الإطارات**: 30 FPS
- **التنسيق**: MP4 (H.264)

### موضوعات الشرائح / Slide Topics

1. **العنوان الرئيسي** - جفاف مياه دجلة والفرات في العراق
2. **مقدمة تاريخية** - بلاد الرافدين: مهد الحضارة
3. **الوضع الحالي** - أزمة مياه حادة
4. **الأسباب الخارجية** - السدود التركية والإيرانية
5. **الأسباب الداخلية** - التغير المناخي وسوء الإدارة
6. **التأثير الزراعي** - تراجع الإنتاج الزراعي
7. **التأثيرات الاجتماعية** - النزوح والبطالة
8. **تأثير الكهرباء** - انخفاض الإنتاج الكهرومائي
9. **الحلول المقترحة** - التفاوض والإدارة المحسنة
10. **الحلول التقنية** - تحلية المياه والتقنيات الحديثة
11. **دور المجتمع الدولي** - الضغط والمساعدات
12. **الخاتمة** - إنقاذ بلاد الرافدين

### الملفات المصدرية / Source Files

#### 1. `tigris_euphrates_drought_presentation.py`
سكريبت Python لإنشاء العرض التقديمي باستخدام مكتبة python-pptx

#### 2. `pptx_to_video_converter.py`
سكريبت Python لتحويل العرض التقديمي إلى فيديو باستخدام مكتبات PIL و MoviePy

#### 3. `main_tigris_euphrates_project.py`
السكريبت الرئيسي الذي يجمع كل العمليات معاً

### المتطلبات التقنية / Technical Requirements

```bash
pip install python-pptx Pillow moviepy numpy
```

### كيفية التشغيل / How to Run

#### الطريقة الأولى: تشغيل السكريبت الرئيسي
```bash
python main_tigris_euphrates_project.py
```

#### الطريقة الثانية: تشغيل كل سكريبت منفصلاً
```bash
# إنشاء العرض التقديمي
python tigris_euphrates_drought_presentation.py

# تحويل العرض إلى فيديو
python pptx_to_video_converter.py
```

### المعلومات المستخدمة / Information Sources

تم جمع المعلومات من مصادر موثوقة تشمل:
- BBC Arabic
- Deutsche Welle (DW)
- Fanack Water
- تقارير الأمم المتحدة
- وزارة الموارد المائية العراقية

### الإحصائيات الرئيسية / Key Statistics

- انخفاض منسوب المياه: 29% في دجلة، 73% في الفرات
- عدد المتضررين: 7 ملايين عراقي
- النازحون: أكثر من 400 ألف شخص
- تراجع المساحات المزروعة: 70%
- عدد السدود التركية: 22 سداً
- التهديد بالجفاف الكامل: بحلول عام 2040

### الألوان المستخدمة / Color Scheme

- **الأزرق الداكن**: (0, 51, 102) - للعناوين الرئيسية
- **الأحمر الداكن**: (139, 0, 0) - للمشاكل والتحديات
- **الأخضر**: (34, 139, 34) - للحلول والمقترحات
- **الذهبي**: (218, 165, 32) - للخاتمة والتأكيدات
- **ألوان فاتحة متنوعة** - للخلفيات

### الميزات التقنية / Technical Features

- **دعم النص العربي**: استخدام خطوط عربية عالية الجودة
- **تصميم متجاوب**: مناسب للعرض على شاشات مختلفة
- **جودة عالية**: دقة Full HD للفيديو
- **تنسيق احترافي**: ألوان وتخطيط مناسب للموضوع
- **سهولة التشغيل**: سكريبتات Python بسيطة ومفهومة

### الاستخدام المقترح / Suggested Usage

- **التعليم الأكاديمي**: في الجامعات والمعاهد
- **التوعية العامة**: في وسائل الإعلام والمؤتمرات
- **البحث العلمي**: كمرجع للدراسات البيئية
- **السياسة العامة**: لصناع القرار والمسؤولين
- **المنظمات الدولية**: للتوعية بالأزمة

### ملاحظات مهمة / Important Notes

1. **الدقة العلمية**: جميع الإحصائيات مأخوذة من مصادر موثوقة
2. **الحساسية الثقافية**: مراعاة الجوانب الثقافية والسياسية
3. **التحديث المستمر**: يمكن تحديث البيانات حسب آخر التطورات
4. **التوافق التقني**: يعمل على أنظمة Windows, Mac, Linux

### المساهمة / Contributing

لتحسين المشروع أو إضافة معلومات جديدة:
1. تحديث البيانات في ملفات Python
2. إضافة شرائح جديدة حسب الحاجة
3. تحسين التصميم والألوان
4. ترجمة المحتوى للغات أخرى

### الترخيص / License

هذا المشروع مفتوح المصدر ويمكن استخدامه لأغراض تعليمية وتوعوية.

---

**تاريخ الإنشاء**: يوليو 2025  
**الإصدار**: 1.0  
**المطور**: Augment Agent  
**اللغة**: العربية مع دعم الإنجليزية
