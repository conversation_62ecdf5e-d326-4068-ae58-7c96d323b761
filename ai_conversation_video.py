#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI-Style Short Video Generator
Creates realistic conversation videos between two characters
For YouTube Shorts and social media entertainment
"""

import os
import sys
from moviepy.editor import *
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import numpy as np
import pyttsx3
import threading
import time
from datetime import datetime
import random

class AIConversationVideo:
    """AI-Style Conversation Video Generator"""

    def __init__(self):
        # Video settings for YouTube Shorts (9:16 aspect ratio)
        self.width = 1080
        self.height = 1920
        self.fps = 30
        self.duration = 55  # Under 60 seconds

        # Colors and styling
        self.bg_colors = [
            (20, 25, 40),    # Dark blue
            (25, 20, 40),    # Dark purple
            (40, 20, 25),    # Dark red
            (20, 40, 25),    # Dark green
        ]

        # Character settings
        self.characters = {
            'person1': {
                'name': 'أحمد',
                'voice_rate': 180,
                'voice_volume': 0.9,
                'color': (100, 200, 255),  # Light blue
                'position': 'left'
            },
            'person2': {
                'name': 'سارة',
                'voice_rate': 200,
                'voice_volume': 0.8,
                'color': (255, 150, 200),  # Light pink
                'position': 'right'
            }
        }

        # Conversation data
        self.conversation = [
            {
                'speaker': 'person1',
                'text': 'سارة، شفتي آخر فيديو للذكاء الاصطناعي؟',
                'duration': 3.5
            },
            {
                'speaker': 'person2',
                'text': 'أي واحد؟ كلهم يقولون نفس الشي!',
                'duration': 3.0
            },
            {
                'speaker': 'person1',
                'text': 'هذا اللي يقول الذكاء الاصطناعي راح يحل كل مشاكل العالم',
                'duration': 4.0
            },
            {
                'speaker': 'person2',
                'text': 'وأنا ما أقدر أخلي الذكاء الاصطناعي يفهم إني أريد قهوة بدون سكر!',
                'duration': 4.5
            },
            {
                'speaker': 'person1',
                'text': 'هههه صحيح! يقولك راح نطير للمريخ',
                'duration': 3.5
            },
            {
                'speaker': 'person2',
                'text': 'وأنا أريد أطير لأقرب مقهى بس!',
                'duration': 3.0
            },
            {
                'speaker': 'person1',
                'text': 'المستقبل مخيف... أو مضحك، ما أدري!',
                'duration': 3.5
            },
            {
                'speaker': 'person2',
                'text': 'المهم نضحك قبل ما الروبوتات تاخذ شغلنا!',
                'duration': 4.0
            }
        ]

        # Initialize TTS engine
        self.tts_engine = pyttsx3.init()
        self.setup_tts()

    def setup_tts(self):
        """Setup text-to-speech engine"""
        try:
            # Get available voices
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to find Arabic voice or use default
                for voice in voices:
                    if 'arabic' in voice.name.lower() or 'ar' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
        except:
            print("⚠️ Using default TTS voice")

    def create_audio_for_text(self, text, character, filename):
        """Generate audio file for given text and character"""
        try:
            # Set voice properties for character
            self.tts_engine.setProperty('rate', self.characters[character]['voice_rate'])
            self.tts_engine.setProperty('volume', self.characters[character]['voice_volume'])

            # Generate audio
            self.tts_engine.save_to_file(text, filename)
            self.tts_engine.runAndWait()

            return True
        except Exception as e:
            print(f"❌ Error generating audio: {e}")
            return False

    def create_animated_background(self, duration):
        """Create animated gradient background"""
        def make_frame(t):
            # Create gradient background
            img = np.zeros((self.height, self.width, 3), dtype=np.uint8)

            # Animated gradient
            color1 = self.bg_colors[int(t * 0.5) % len(self.bg_colors)]
            color2 = self.bg_colors[int(t * 0.3 + 1) % len(self.bg_colors)]

            for y in range(self.height):
                ratio = y / self.height
                # Smooth color interpolation
                r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
                g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
                b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
                img[y, :] = [r, g, b]

            # Add floating particles
            num_particles = 20
            for i in range(num_particles):
                x = int((np.sin(t * 2 + i) * 0.3 + 0.5) * self.width)
                y = int((np.cos(t * 1.5 + i * 0.5) * 0.4 + 0.5) * self.height)
                size = int(3 + 2 * np.sin(t * 3 + i))

                # Draw particle
                if 0 <= x < self.width and 0 <= y < self.height:
                    for dx in range(-size, size + 1):
                        for dy in range(-size, size + 1):
                            if 0 <= x + dx < self.width and 0 <= y + dy < self.height:
                                if dx*dx + dy*dy <= size*size:
                                    img[y + dy, x + dx] = [255, 255, 255]

            return img

        return VideoClip(make_frame, duration=duration)

    def create_text_clip(self, text, character, duration, start_time):
        """Create animated text clip for character"""
        # Character settings
        char_info = self.characters[character]
        color = char_info['color']
        position = char_info['position']

        # Create text image
        img = Image.new('RGBA', (self.width, 200), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Try to load Arabic font
        try:
            font = ImageFont.truetype("arial.ttf", 45)
        except:
            font = ImageFont.load_default()

        # Get text size
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # Position text based on character
        if position == 'left':
            x = 50
            y = 50
        else:
            x = self.width - text_width - 50
            y = 50

        # Draw text with outline
        outline_color = (0, 0, 0, 255)
        for dx in [-2, -1, 0, 1, 2]:
            for dy in [-2, -1, 0, 1, 2]:
                if dx != 0 or dy != 0:
                    draw.text((x + dx, y + dy), text, font=font, fill=outline_color)

        # Draw main text
        draw.text((x, y), text, font=font, fill=(*color, 255))

        # Convert to numpy array
        img_array = np.array(img)

        # Create video clip
        def make_frame(t):
            # Typing animation
            chars_to_show = int((t / duration) * len(text))
            if chars_to_show >= len(text):
                return img_array

            # Create partial text image
            partial_img = Image.new('RGBA', (self.width, 200), (0, 0, 0, 0))
            partial_draw = ImageDraw.Draw(partial_img)
            partial_text = text[:chars_to_show]

            # Draw outline
            for dx in [-2, -1, 0, 1, 2]:
                for dy in [-2, -1, 0, 1, 2]:
                    if dx != 0 or dy != 0:
                        partial_draw.text((x + dx, y + dy), partial_text, font=font, fill=outline_color)

            # Draw main text
            partial_draw.text((x, y), partial_text, font=font, fill=(*color, 255))

            return np.array(partial_img)

        clip = VideoClip(make_frame, duration=duration)

        # Position on screen
        if position == 'left':
            clip = clip.set_position(('left', 'top'))
        else:
            clip = clip.set_position(('right', 'top'))

        return clip.set_start(start_time)

    def create_character_indicator(self, character, duration, start_time):
        """Create character name indicator"""
        char_info = self.characters[character]
        name = char_info['name']
        color = char_info['color']
        position = char_info['position']

        # Create indicator image
        img = Image.new('RGBA', (200, 60), (0, 0, 0, 180))
        draw = ImageDraw.Draw(img)

        try:
            font = ImageFont.truetype("arial.ttf", 25)
        except:
            font = ImageFont.load_default()

        # Draw character name
        bbox = draw.textbbox((0, 0), name, font=font)
        text_width = bbox[2] - bbox[0]
        x = (200 - text_width) // 2
        y = 15

        draw.text((x, y), name, font=font, fill=(*color, 255))

        # Convert to clip
        clip = ImageClip(np.array(img), duration=duration)

        # Position based on character
        if position == 'left':
            clip = clip.set_position((50, self.height - 300))
        else:
            clip = clip.set_position((self.width - 250, self.height - 300))

        return clip.set_start(start_time)

    def create_video(self):
        """Create the complete AI conversation video"""
        print("🎬 Creating AI-Style Conversation Video...")

        # Create animated background
        print("🎨 Creating animated background...")
        background = self.create_animated_background(self.duration)

        # Create all clips
        all_clips = [background]
        current_time = 0

        # Generate audio and text clips for each conversation part
        for i, conv in enumerate(self.conversation):
            speaker = conv['speaker']
            text = conv['text']
            duration = conv['duration']

            print(f"🎤 Processing dialogue {i+1}: {speaker}")

            # Generate audio file
            audio_filename = f"temp_audio_{i}_{speaker}.wav"
            if self.create_audio_for_text(text, speaker, audio_filename):
                # Create audio clip
                try:
                    audio_clip = AudioFileClip(audio_filename)
                    # Adjust duration to match our timing
                    if audio_clip.duration > duration:
                        audio_clip = audio_clip.subclip(0, duration)
                    audio_clip = audio_clip.set_start(current_time)
                    all_clips.append(audio_clip)
                except:
                    print(f"⚠️ Could not load audio for {speaker}")

            # Create text clip
            text_clip = self.create_text_clip(text, speaker, duration, current_time)
            all_clips.append(text_clip)

            # Create character indicator
            indicator_clip = self.create_character_indicator(speaker, duration, current_time)
            all_clips.append(indicator_clip)

            current_time += duration

        # Add title and outro
        title_clip = self.create_title_clip()
        all_clips.append(title_clip)

        outro_clip = self.create_outro_clip(current_time)
        all_clips.append(outro_clip)

        # Combine all clips
        print("🎥 Combining all clips...")
        final_video = CompositeVideoClip(all_clips, size=(self.width, self.height))
        final_video = final_video.set_duration(self.duration)
        final_video = final_video.set_fps(self.fps)

        # Export video
        output_filename = f"AI_Conversation_Short_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"
        print(f"💾 Exporting video as {output_filename}...")

        final_video.write_videofile(
            output_filename,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '23']  # Good quality for social media
        )

        # Clean up temporary audio files
        print("🧹 Cleaning up temporary files...")
        for i in range(len(self.conversation)):
            for speaker in ['person1', 'person2']:
                temp_file = f"temp_audio_{i}_{speaker}.wav"
                try:
                    os.remove(temp_file)
                except:
                    pass

        print(f"\n🎉 Successfully created AI conversation video: {output_filename}")
        print("\n📱 Video specifications:")
        print(f"• Resolution: {self.width}x{self.height} (9:16 for YouTube Shorts)")
        print(f"• Duration: {self.duration} seconds")
        print(f"• Frame rate: {self.fps} FPS")
        print("• Format: MP4 (H.264)")
        print("• Audio: AAC")
        print("\n🚀 Ready for upload to:")
        print("• YouTube Shorts")
        print("• TikTok")
        print("• Instagram Reels")
        print("• Facebook Stories")

        return output_filename

    def create_title_clip(self):
        """Create animated title clip"""
        # Create title image
        img = Image.new('RGBA', (self.width, 300), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        title_text = "محادثة ذكية 🤖"
        subtitle_text = "بين الواقع والخيال"

        try:
            title_font = ImageFont.truetype("arial.ttf", 60)
            subtitle_font = ImageFont.truetype("arial.ttf", 35)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        # Get text sizes
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]

        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]

        # Center positions
        title_x = (self.width - title_width) // 2
        title_y = 100
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = 180

        # Draw title with glow effect
        glow_color = (255, 255, 0, 100)
        text_color = (255, 255, 255, 255)

        # Glow effect
        for offset in range(1, 5):
            draw.text((title_x + offset, title_y + offset), title_text,
                     font=title_font, fill=glow_color)
            draw.text((title_x - offset, title_y - offset), title_text,
                     font=title_font, fill=glow_color)

        # Main title
        draw.text((title_x, title_y), title_text, font=title_font, fill=text_color)
        draw.text((subtitle_x, subtitle_y), subtitle_text, font=subtitle_font,
                 fill=(200, 200, 200, 255))

        # Create animated clip
        def make_frame(t):
            if t < 2:
                # Fade in
                alpha = int(255 * (t / 2))
                fade_img = img.copy()
                fade_img.putalpha(alpha)
                return np.array(fade_img)
            elif t > 3:
                # Fade out
                alpha = int(255 * (1 - (t - 3) / 1))
                fade_img = img.copy()
                fade_img.putalpha(alpha)
                return np.array(fade_img)
            else:
                return np.array(img)

        clip = VideoClip(make_frame, duration=4)
        return clip.set_position('center').set_start(0)

    def create_outro_clip(self, start_time):
        """Create outro clip with call-to-action"""
        # Create outro image
        img = Image.new('RGBA', (self.width, 400), (0, 0, 0, 200))
        draw = ImageDraw.Draw(img)

        outro_text = "شكراً للمشاهدة! 👍"
        cta_text = "اشترك للمزيد من المحتوى المسلي"

        try:
            outro_font = ImageFont.truetype("arial.ttf", 50)
            cta_font = ImageFont.truetype("arial.ttf", 30)
        except:
            outro_font = ImageFont.load_default()
            cta_font = ImageFont.load_default()

        # Get text sizes and center
        outro_bbox = draw.textbbox((0, 0), outro_text, font=outro_font)
        outro_width = outro_bbox[2] - outro_bbox[0]
        outro_x = (self.width - outro_width) // 2
        outro_y = 150

        cta_bbox = draw.textbbox((0, 0), cta_text, font=cta_font)
        cta_width = cta_bbox[2] - cta_bbox[0]
        cta_x = (self.width - cta_width) // 2
        cta_y = 220

        # Draw text
        draw.text((outro_x, outro_y), outro_text, font=outro_font,
                 fill=(255, 255, 255, 255))
        draw.text((cta_x, cta_y), cta_text, font=cta_font,
                 fill=(255, 200, 100, 255))

        clip = ImageClip(np.array(img), duration=3)
        return clip.set_position('center').set_start(start_time)


def main():
    """Main function to create the AI conversation video"""
    try:
        print("🤖 AI Conversation Video Generator")
        print("=" * 50)

        # Create video generator instance
        generator = AIConversationVideo()

        # Generate the video
        video_file = generator.create_video()

        print(f"\n✅ Video creation completed successfully!")
        print(f"📁 Output file: {video_file}")
        print("\n🎯 Perfect for:")
        print("• YouTube Shorts")
        print("• TikTok videos")
        print("• Instagram Reels")
        print("• Facebook Stories")
        print("• WhatsApp Status")

    except Exception as e:
        print(f"❌ Error creating video: {str(e)}")
        print("\n🔧 Make sure you have installed:")
        print("pip install moviepy pillow pyttsx3 numpy")


if __name__ == "__main__":
    main()