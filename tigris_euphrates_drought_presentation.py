#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تقديمي حول جفاف مياه دجلة والفرات في العراق
PowerPoint Presentation about Tigris and Euphrates Drought in Iraq
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def create_tigris_euphrates_presentation():
    """إنشاء عرض تقديمي حول جفاف مياه دجلة والفرات"""

    # إنشاء عرض تقديمي جديد
    prs = Presentation()

    # تعيين حجم الشرائح (16:9)
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)

    # الألوان المستخدمة
    blue_color = RGBColor(0, 51, 102)      # أزرق داكن
    red_color = RGBColor(139, 0, 0)        # أحمر داكن
    gold_color = RGBColor(218, 165, 32)    # ذهبي
    white_color = RGBColor(255, 255, 255)  # أبيض

    # الشريحة الأولى: العنوان الرئيسي
    slide1 = prs.slides.add_slide(prs.slide_layouts[6])  # تخطيط فارغ

    # خلفية زرقاء داكنة
    background = slide1.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background.fill.solid()
    background.fill.fore_color.rgb = blue_color
    background.line.fill.background()

    # العنوان الرئيسي
    title_box = slide1.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(1.5))
    title_frame = title_box.text_frame
    title_frame.text = "جفاف مياه دجلة والفرات في العراق"
    title_para = title_frame.paragraphs[0]
    title_para.alignment = PP_ALIGN.CENTER
    title_para.font.name = "Amiri"
    title_para.font.size = Pt(48)
    title_para.font.color.rgb = gold_color
    title_para.font.bold = True

    # العنوان الفرعي
    subtitle_box = slide1.shapes.add_textbox(Inches(1), Inches(4), Inches(11.33), Inches(1))
    subtitle_frame = subtitle_box.text_frame
    subtitle_frame.text = "أزمة المياه في بلاد الرافدين"
    subtitle_para = subtitle_frame.paragraphs[0]
    subtitle_para.alignment = PP_ALIGN.CENTER
    subtitle_para.font.name = "Amiri"
    subtitle_para.font.size = Pt(32)
    subtitle_para.font.color.rgb = white_color

    # الشريحة الثانية: مقدمة تاريخية
    slide2 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية
    background2 = slide2.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background2.fill.solid()
    background2.fill.fore_color.rgb = RGBColor(245, 245, 245)
    background2.line.fill.background()

    # عنوان الشريحة
    title2_box = slide2.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title2_frame = title2_box.text_frame
    title2_frame.text = "بلاد الرافدين: مهد الحضارة"
    title2_para = title2_frame.paragraphs[0]
    title2_para.alignment = PP_ALIGN.CENTER
    title2_para.font.name = "Amiri"
    title2_para.font.size = Pt(36)
    title2_para.font.color.rgb = blue_color
    title2_para.font.bold = True

    # المحتوى
    content2_box = slide2.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content2_frame = content2_box.text_frame
    content2_frame.text = """• نهرا دجلة والفرات: شريان الحياة في العراق منذ آلاف السنين
• مهد الحضارات القديمة: بابل وآشور وسومر
• مصدر المياه الرئيسي للزراعة والشرب والصناعة
• يغذي النهران أكثر من 40 مليون نسمة في المنطقة
• العراق يعتمد على هذين النهرين بنسبة 98% من موارده المائية"""

    for para in content2_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(51, 51, 51)
        para.space_after = Pt(12)

    # الشريحة الثالثة: الوضع الحالي
    slide3 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية حمراء فاتحة
    background3 = slide3.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background3.fill.solid()
    background3.fill.fore_color.rgb = RGBColor(255, 240, 240)
    background3.line.fill.background()

    # عنوان الشريحة
    title3_box = slide3.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title3_frame = title3_box.text_frame
    title3_frame.text = "الوضع الحالي: أزمة مياه حادة"
    title3_para = title3_frame.paragraphs[0]
    title3_para.alignment = PP_ALIGN.CENTER
    title3_para.font.name = "Amiri"
    title3_para.font.size = Pt(36)
    title3_para.font.color.rgb = red_color
    title3_para.font.bold = True

    # المحتوى
    content3_box = slide3.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content3_frame = content3_box.text_frame
    content3_frame.text = """• انخفاض منسوب المياه بنسبة 29% في دجلة و73% في الفرات
• تحول أجزاء من النهرين إلى برك من مياه الصرف الصحي
• تهديد بجفاف كامل للنهرين بحلول عام 2040
• تأثر 7 ملايين عراقي بنقص المياه الصالحة للشرب
• انخفاض الإنتاج الزراعي بنسبة تزيد عن 50%"""

    for para in content3_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = red_color
        para.space_after = Pt(12)

    # الشريحة الرابعة: الأسباب الخارجية
    slide4 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية
    background4 = slide4.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background4.fill.solid()
    background4.fill.fore_color.rgb = RGBColor(240, 248, 255)
    background4.line.fill.background()

    # عنوان الشريحة
    title4_box = slide4.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title4_frame = title4_box.text_frame
    title4_frame.text = "الأسباب الخارجية للأزمة"
    title4_para = title4_frame.paragraphs[0]
    title4_para.alignment = PP_ALIGN.CENTER
    title4_para.font.name = "Amiri"
    title4_para.font.size = Pt(36)
    title4_para.font.color.rgb = blue_color
    title4_para.font.bold = True

    # المحتوى
    content4_box = slide4.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content4_frame = content4_box.text_frame
    content4_frame.text = """• السدود التركية: 22 سداً على نهري دجلة والفرات
• سد أليسو التركي: يحجب 50% من مياه دجلة
• السدود الإيرانية: تحويل مجرى روافد دجلة
• قطع إيران لنهري الزاب الصغير وسيروان
• عدم التزام الدول المجاورة بالاتفاقيات المائية الدولية"""

    for para in content4_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(51, 51, 51)
        para.space_after = Pt(12)

    # الشريحة الخامسة: الأسباب الداخلية والمناخية
    slide5 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية
    background5 = slide5.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background5.fill.solid()
    background5.fill.fore_color.rgb = RGBColor(255, 248, 220)
    background5.line.fill.background()

    # عنوان الشريحة
    title5_box = slide5.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title5_frame = title5_box.text_frame
    title5_frame.text = "الأسباب الداخلية والمناخية"
    title5_para = title5_frame.paragraphs[0]
    title5_para.alignment = PP_ALIGN.CENTER
    title5_para.font.name = "Amiri"
    title5_para.font.size = Pt(36)
    title5_para.font.color.rgb = RGBColor(184, 134, 11)
    title5_para.font.bold = True

    # المحتوى
    content5_box = slide5.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content5_frame = content5_box.text_frame
    content5_frame.text = """• التغير المناخي: ارتفاع درجات الحرارة وقلة الأمطار
• موسم 2020-2021: ثاني أكثر المواسم جفافاً منذ 40 عاماً
• سوء إدارة الموارد المائية المحلية
• تلوث المياه بسبب مياه الصرف الصحي والنفايات الصناعية
• الاستخدام المفرط للمياه الجوفية"""

    for para in content5_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(139, 69, 19)
        para.space_after = Pt(12)

    # الشريحة السادسة: التأثيرات على الزراعة
    slide6 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية خضراء فاتحة
    background6 = slide6.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background6.fill.solid()
    background6.fill.fore_color.rgb = RGBColor(240, 255, 240)
    background6.line.fill.background()

    # عنوان الشريحة
    title6_box = slide6.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title6_frame = title6_box.text_frame
    title6_frame.text = "التأثير على القطاع الزراعي"
    title6_para = title6_frame.paragraphs[0]
    title6_para.alignment = PP_ALIGN.CENTER
    title6_para.font.name = "Amiri"
    title6_para.font.size = Pt(36)
    title6_para.font.color.rgb = RGBColor(34, 139, 34)
    title6_para.font.bold = True

    # المحتوى
    content6_box = slide6.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content6_frame = content6_box.text_frame
    content6_frame.text = """• تراجع المساحات المزروعة بنسبة 70%
• انخفاض إنتاج القمح والشعير والأرز
• هجرة المزارعين من الريف إلى المدن
• تدهور الأراضي الزراعية وتصحرها
• فقدان العراق لاكتفائه الذاتي من المحاصيل الأساسية"""

    for para in content6_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(0, 100, 0)
        para.space_after = Pt(12)

    # الشريحة السابعة: التأثيرات الاجتماعية والاقتصادية
    slide7 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية
    background7 = slide7.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background7.fill.solid()
    background7.fill.fore_color.rgb = RGBColor(255, 245, 238)
    background7.line.fill.background()

    # عنوان الشريحة
    title7_box = slide7.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title7_frame = title7_box.text_frame
    title7_frame.text = "التأثيرات الاجتماعية والاقتصادية"
    title7_para = title7_frame.paragraphs[0]
    title7_para.alignment = PP_ALIGN.CENTER
    title7_para.font.name = "Amiri"
    title7_para.font.size = Pt(36)
    title7_para.font.color.rgb = RGBColor(205, 92, 92)
    title7_para.font.bold = True

    # المحتوى
    content7_box = slide7.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content7_frame = content7_box.text_frame
    content7_frame.text = """• نزوح أكثر من 400 ألف شخص من المناطق المتضررة
• ارتفاع معدلات البطالة في القطاع الزراعي
• تدهور الأوضاع الصحية بسبب تلوث المياه
• زيادة النزاعات المحلية حول الموارد المائية المتبقية
• تهديد الأمن الغذائي والاستقرار الاجتماعي"""

    for para in content7_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(139, 69, 19)
        para.space_after = Pt(12)

    # الشريحة الثامنة: تأثير على توليد الكهرباء
    slide8 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية صفراء فاتحة
    background8 = slide8.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background8.fill.solid()
    background8.fill.fore_color.rgb = RGBColor(255, 255, 224)
    background8.line.fill.background()

    # عنوان الشريحة
    title8_box = slide8.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title8_frame = title8_box.text_frame
    title8_frame.text = "تأثير على قطاع الكهرباء"
    title8_para = title8_frame.paragraphs[0]
    title8_para.alignment = PP_ALIGN.CENTER
    title8_para.font.name = "Amiri"
    title8_para.font.size = Pt(36)
    title8_para.font.color.rgb = RGBColor(255, 140, 0)
    title8_para.font.bold = True

    # المحتوى
    content8_box = slide8.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content8_frame = content8_box.text_frame
    content8_frame.text = """• انخفاض إنتاج الكهرباء من السدود المائية
• توقف عدة محطات توليد كهرومائية عن العمل
• زيادة الاعتماد على الوقود الأحفوري المكلف
• انقطاع متكرر للتيار الكهربائي في المناطق المتضررة
• تأثير سلبي على الصناعات التي تعتمد على الكهرباء"""

    for para in content8_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(255, 69, 0)
        para.space_after = Pt(12)

    # الشريحة التاسعة: الحلول المقترحة
    slide9 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية خضراء
    background9 = slide9.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background9.fill.solid()
    background9.fill.fore_color.rgb = RGBColor(240, 255, 240)
    background9.line.fill.background()

    # عنوان الشريحة
    title9_box = slide9.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title9_frame = title9_box.text_frame
    title9_frame.text = "الحلول والمقترحات"
    title9_para = title9_frame.paragraphs[0]
    title9_para.alignment = PP_ALIGN.CENTER
    title9_para.font.name = "Amiri"
    title9_para.font.size = Pt(36)
    title9_para.font.color.rgb = RGBColor(34, 139, 34)
    title9_para.font.bold = True

    # المحتوى
    content9_box = slide9.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content9_frame = content9_box.text_frame
    content9_frame.text = """• التفاوض مع تركيا وإيران لضمان حصة عادلة من المياه
• تطبيق الاتفاقيات المائية الدولية
• تحسين كفاءة استخدام المياه في الزراعة
• تطوير تقنيات الري الحديثة والذكية
• معالجة مياه الصرف الصحي وإعادة استخدامها"""

    for para in content9_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(0, 100, 0)
        para.space_after = Pt(12)

    # الشريحة العاشرة: الحلول التقنية
    slide10 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية زرقاء فاتحة
    background10 = slide10.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background10.fill.solid()
    background10.fill.fore_color.rgb = RGBColor(230, 244, 255)
    background10.line.fill.background()

    # عنوان الشريحة
    title10_box = slide10.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title10_frame = title10_box.text_frame
    title10_frame.text = "الحلول التقنية والبديلة"
    title10_para = title10_frame.paragraphs[0]
    title10_para.alignment = PP_ALIGN.CENTER
    title10_para.font.name = "Amiri"
    title10_para.font.size = Pt(36)
    title10_para.font.color.rgb = blue_color
    title10_para.font.bold = True

    # المحتوى
    content10_box = slide10.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content10_frame = content10_box.text_frame
    content10_frame.text = """• تحلية مياه البحر من الخليج العربي
• حفر آبار جوفية عميقة بتقنيات حديثة
• بناء سدود وخزانات مائية محلية
• استخدام تقنيات الذكاء الاصطناعي لإدارة المياه
• تطوير مصادر طاقة متجددة لتشغيل محطات التحلية"""

    for para in content10_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(25, 25, 112)
        para.space_after = Pt(12)

    # الشريحة الحادية عشرة: دور المجتمع الدولي
    slide11 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية بنفسجية فاتحة
    background11 = slide11.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background11.fill.solid()
    background11.fill.fore_color.rgb = RGBColor(248, 248, 255)
    background11.line.fill.background()

    # عنوان الشريحة
    title11_box = slide11.shapes.add_textbox(Inches(1), Inches(0.5), Inches(11.33), Inches(1))
    title11_frame = title11_box.text_frame
    title11_frame.text = "دور المجتمع الدولي"
    title11_para = title11_frame.paragraphs[0]
    title11_para.alignment = PP_ALIGN.CENTER
    title11_para.font.name = "Amiri"
    title11_para.font.size = Pt(36)
    title11_para.font.color.rgb = RGBColor(75, 0, 130)
    title11_para.font.bold = True

    # المحتوى
    content11_box = slide11.shapes.add_textbox(Inches(1), Inches(2), Inches(11.33), Inches(4.5))
    content11_frame = content11_box.text_frame
    content11_frame.text = """• الضغط الدولي على تركيا وإيران لاحترام حقوق المياه
• تقديم المساعدات التقنية والمالية للعراق
• دعم مشاريع البنية التحتية المائية
• تطبيق القانون الدولي للمياه العابرة للحدود
• التعاون في مواجهة تحديات التغير المناخي"""

    for para in content11_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(24)
        para.font.color.rgb = RGBColor(72, 61, 139)
        para.space_after = Pt(12)

    # الشريحة الثانية عشرة: الخاتمة
    slide12 = prs.slides.add_slide(prs.slide_layouts[6])

    # خلفية ذهبية
    background12 = slide12.shapes.add_shape(
        MSO_SHAPE.RECTANGLE, 0, 0, prs.slide_width, prs.slide_height
    )
    background12.fill.solid()
    background12.fill.fore_color.rgb = RGBColor(255, 248, 220)
    background12.line.fill.background()

    # عنوان الشريحة
    title12_box = slide12.shapes.add_textbox(Inches(1), Inches(1), Inches(11.33), Inches(1.5))
    title12_frame = title12_box.text_frame
    title12_frame.text = "الخاتمة: إنقاذ بلاد الرافدين"
    title12_para = title12_frame.paragraphs[0]
    title12_para.alignment = PP_ALIGN.CENTER
    title12_para.font.name = "Amiri"
    title12_para.font.size = Pt(40)
    title12_para.font.color.rgb = RGBColor(184, 134, 11)
    title12_para.font.bold = True

    # المحتوى
    content12_box = slide12.shapes.add_textbox(Inches(1), Inches(3), Inches(11.33), Inches(3.5))
    content12_frame = content12_box.text_frame
    content12_frame.text = """أزمة جفاف دجلة والفرات تهدد مستقبل العراق وحضارته العريقة.

الحل يتطلب تضافر الجهود المحلية والإقليمية والدولية لضمان:
• حق العراق في حصة عادلة من مياه النهرين
• تطوير بدائل مائية مستدامة
• حماية البيئة والتراث الحضاري لبلاد الرافدين

المياه حق إنساني أساسي وليس سلاحاً سياسياً"""

    for para in content12_frame.paragraphs:
        para.font.name = "Amiri"
        para.font.size = Pt(22)
        para.font.color.rgb = RGBColor(139, 69, 19)
        para.space_after = Pt(8)
        para.alignment = PP_ALIGN.CENTER

    # حفظ العرض التقديمي
    prs.save("جفاف_دجلة_والفرات_في_العراق.pptx")
    print("تم إنشاء العرض التقديمي بنجاح!")
    return "جفاف_دجلة_والفرات_في_العراق.pptx"

if __name__ == "__main__":
    presentation_file = create_tigris_euphrates_presentation()
    print(f"تم حفظ العرض التقديمي في: {presentation_file}")
