#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Video from Frames
إنشاء فيديو من الإطارات المُنشأة
"""

import os
import sys
import glob
from datetime import datetime

def install_moviepy():
    """Install compatible moviepy version"""
    try:
        import subprocess
        print("Installing compatible MoviePy version...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "moviepy==1.0.3", "--force-reinstall"])
        print("MoviePy installed successfully!")
        return True
    except Exception as e:
        print(f"Failed to install MoviePy: {e}")
        return False

def create_video_with_moviepy():
    """Create video using MoviePy"""
    try:
        # Try to import moviepy
        try:
            from moviepy.editor import ImageClip, concatenate_videoclips
        except ImportError:
            print("MoviePy not found. Installing...")
            if not install_moviepy():
                return False
            from moviepy.editor import ImageClip, concatenate_videoclips
        
        # Get all frame files
        frames_dir = "excel_video_frames"
        frame_files = sorted(glob.glob(os.path.join(frames_dir, "*.png")))
        
        if not frame_files:
            print("No frame files found!")
            return False
        
        print(f"Found {len(frame_files)} frames")
        
        # Create video clips
        clips = []
        frame_duration = 5  # 5 seconds per frame
        
        for i, frame_file in enumerate(frame_files):
            print(f"Processing frame {i+1}/{len(frame_files)}: {os.path.basename(frame_file)}")
            try:
                clip = ImageClip(frame_file, duration=frame_duration)
                clips.append(clip)
            except Exception as e:
                print(f"Error processing frame {frame_file}: {e}")
                continue
        
        if not clips:
            print("No valid clips created!")
            return False
        
        print(f"Created {len(clips)} clips. Concatenating...")
        
        # Concatenate clips
        final_video = concatenate_videoclips(clips, method="compose")
        
        # Output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"العلاقات_الكويتية_العراقية_فيديو_{timestamp}.mp4"
        
        print(f"Writing video to {output_filename}...")
        
        # Write video file
        final_video.write_videofile(
            output_filename,
            fps=30,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        print(f"✅ Video created successfully: {output_filename}")
        print(f"📁 Video duration: {len(clips) * frame_duration} seconds")
        
        # Clean up
        for clip in clips:
            clip.close()
        final_video.close()
        
        return output_filename
        
    except Exception as e:
        print(f"Error creating video with MoviePy: {e}")
        return False

def create_video_with_ffmpeg():
    """Create video using FFmpeg if available"""
    try:
        import subprocess
        
        # Check if ffmpeg is available
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("FFmpeg not found")
            return False
        
        print("FFmpeg found. Creating video...")
        
        frames_dir = "excel_video_frames"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"العلاقات_الكويتية_العراقية_فيديو_{timestamp}.mp4"
        
        # FFmpeg command
        cmd = [
            'ffmpeg',
            '-framerate', '1/5',  # 1 frame every 5 seconds
            '-pattern_type', 'glob',
            '-i', f'{frames_dir}/*.png',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-r', '30',
            '-y',  # Overwrite output file
            output_filename
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Video created successfully with FFmpeg: {output_filename}")
            return output_filename
        else:
            print(f"FFmpeg error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error with FFmpeg: {e}")
        return False

def create_slideshow_instructions():
    """Create detailed instructions for manual video creation"""
    instructions = """
🎬 تعليمات مفصلة لإنشاء الفيديو يدوياً

📋 الطريقة الأولى: استخدام برامج تحرير الفيديو المجانية

1️⃣ DaVinci Resolve (الأفضل - مجاني):
   - حمل من: https://www.blackmagicdesign.com/products/davinciresolve
   - أنشئ مشروع جديد بدقة 1920x1080 و 30 FPS
   - استورد جميع الصور من مجلد excel_video_frames
   - اسحب الصور للتايم لاين بالترتيب
   - اضبط مدة كل صورة على 5 ثواني
   - صدر كـ MP4

2️⃣ OpenShot (سهل الاستخدام - مجاني):
   - حمل من: https://www.openshot.org
   - أنشئ مشروع جديد
   - استورد الصور
   - اسحبها للتايم لاين
   - انقر بالزر الأيمن على كل صورة واختر Properties
   - غير Duration إلى 5.00 ثواني
   - صدر الفيديو

3️⃣ Shotcut (متقدم - مجاني):
   - حمل من: https://shotcut.org
   - أنشئ مشروع جديد
   - اضبط Video Mode على HD 1080p 30 fps
   - استورد الصور
   - اسحبها للتايم لاين
   - اضبط مدة كل صورة على 5 ثواني

📋 الطريقة الثانية: استخدام PowerPoint

1. افتح PowerPoint
2. أنشئ عرض تقديمي جديد
3. استورد كل صورة في شريحة منفصلة
4. اذهب إلى Slide Show > Set Up Slide Show
5. اختر "Browsed at a kiosk" واضبط على 5 ثواني
6. صدر كفيديو: File > Export > Create a Video

📋 الطريقة الثالثة: استخدام مواقع الإنترنت

1. Canva (canva.com):
   - أنشئ حساب مجاني
   - اختر Video template
   - استورد الصور
   - اضبط مدة كل صورة على 5 ثواني
   - حمل الفيديو

2. Kapwing (kapwing.com):
   - ارفع الصور
   - أنشئ slideshow
   - اضبط التوقيت
   - حمل النتيجة

📊 معلومات الفيديو النهائي:
- عدد الإطارات: 21 إطار
- مدة كل إطار: 5 ثواني
- المدة الإجمالية: 105 ثانية (1 دقيقة و 45 ثانية)
- الدقة: 1920x1080 (Full HD)
- معدل الإطارات المقترح: 30 FPS
- الصيغة المقترحة: MP4

📁 مجلد الإطارات: excel_video_frames
🎯 ترتيب الإطارات: frame_000_title.png إلى frame_020_data.png
"""
    
    with open("تعليمات_إنشاء_الفيديو.txt", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("📄 تم إنشاء ملف تعليمات_إنشاء_الفيديو.txt")
    print(instructions)

def main():
    print("🎬 محول الإطارات إلى فيديو")
    print("=" * 50)
    
    # Check if frames exist
    frames_dir = "excel_video_frames"
    if not os.path.exists(frames_dir):
        print(f"❌ مجلد الإطارات غير موجود: {frames_dir}")
        return
    
    frame_files = glob.glob(os.path.join(frames_dir, "*.png"))
    if not frame_files:
        print(f"❌ لا توجد إطارات في المجلد: {frames_dir}")
        return
    
    print(f"✅ تم العثور على {len(frame_files)} إطار")
    
    # Try different methods
    video_created = False
    
    # Method 1: Try MoviePy
    print("\n🎬 المحاولة الأولى: استخدام MoviePy...")
    result = create_video_with_moviepy()
    if result:
        video_created = True
        print(f"✅ تم إنشاء الفيديو بنجاح: {result}")
    
    # Method 2: Try FFmpeg if MoviePy failed
    if not video_created:
        print("\n🎬 المحاولة الثانية: استخدام FFmpeg...")
        result = create_video_with_ffmpeg()
        if result:
            video_created = True
            print(f"✅ تم إنشاء الفيديو بنجاح: {result}")
    
    # If both failed, provide instructions
    if not video_created:
        print("\n❌ فشل في إنشاء الفيديو تلقائياً")
        print("📋 سيتم إنشاء تعليمات للإنشاء اليدوي...")
        create_slideshow_instructions()
    
    print("\n🎉 انتهت العملية!")

if __name__ == "__main__":
    main()
