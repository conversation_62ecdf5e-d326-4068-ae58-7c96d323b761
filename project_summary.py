#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملخص مشروع جفاف مياه دجلة والفرات في العراق
Project Summary: Tigris and Euphrates Drought in Iraq
"""

import os
import datetime

def get_file_info(filename):
    """الحصول على معلومات الملف"""
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        modified = datetime.datetime.fromtimestamp(os.path.getmtime(filename))
        return {
            'exists': True,
            'size': size,
            'size_mb': round(size / (1024 * 1024), 2),
            'modified': modified.strftime('%Y-%m-%d %H:%M:%S')
        }
    else:
        return {'exists': False}

def display_project_summary():
    """عرض ملخص المشروع"""
    
    print("=" * 80)
    print("مشروع جفاف مياه دجلة والفرات في العراق")
    print("Tigris and Euphrates Drought in Iraq Project")
    print("=" * 80)
    
    # قائمة الملفات المتوقعة
    files_to_check = [
        {
            'name': 'جفاف_دجلة_والفرات_في_العراق.pptx',
            'description': 'العرض التقديمي الرئيسي (PowerPoint Presentation)',
            'type': 'presentation'
        },
        {
            'name': 'جفاف_دجلة_والفرات_فيديو.mp4',
            'description': 'الفيديو التعليمي (Educational Video)',
            'type': 'video'
        },
        {
            'name': 'tigris_euphrates_drought_presentation.py',
            'description': 'سكريبت إنشاء العرض التقديمي',
            'type': 'script'
        },
        {
            'name': 'pptx_to_video_converter.py',
            'description': 'سكريبت تحويل العرض إلى فيديو',
            'type': 'script'
        },
        {
            'name': 'main_tigris_euphrates_project.py',
            'description': 'السكريبت الرئيسي',
            'type': 'script'
        },
        {
            'name': 'README_Tigris_Euphrates_Project.md',
            'description': 'دليل المشروع',
            'type': 'documentation'
        }
    ]
    
    print("\n📁 ملفات المشروع / Project Files:")
    print("-" * 80)
    
    for file_info in files_to_check:
        filename = file_info['name']
        description = file_info['description']
        file_type = file_info['type']
        
        info = get_file_info(filename)
        
        if info['exists']:
            status = "✅ موجود"
            size_info = f"({info['size_mb']} MB)"
            modified_info = f"آخر تعديل: {info['modified']}"
        else:
            status = "❌ غير موجود"
            size_info = ""
            modified_info = ""
        
        print(f"{status} {filename}")
        print(f"   📝 {description}")
        if info['exists']:
            print(f"   📊 الحجم: {size_info}")
            print(f"   🕒 {modified_info}")
        print()
    
    # معلومات إضافية عن المشروع
    print("\n📋 تفاصيل المشروع / Project Details:")
    print("-" * 80)
    print("🎯 الهدف: توعية حول أزمة جفاف نهري دجلة والفرات في العراق")
    print("📊 عدد الشرائح: 12 شريحة")
    print("⏱️ مدة الفيديو: 60 ثانية (5 ثوانٍ لكل شريحة)")
    print("🎨 الدقة: 1920x1080 (Full HD)")
    print("🔤 الخط المستخدم: Amiri (خط عربي عالي الجودة)")
    print("🎬 تنسيق الفيديو: MP4 (H.264, 30 FPS)")
    
    print("\n📚 موضوعات الشرائح / Slide Topics:")
    print("-" * 80)
    topics = [
        "1. العنوان الرئيسي - جفاف مياه دجلة والفرات في العراق",
        "2. مقدمة تاريخية - بلاد الرافدين: مهد الحضارة",
        "3. الوضع الحالي - أزمة مياه حادة",
        "4. الأسباب الخارجية - السدود التركية والإيرانية",
        "5. الأسباب الداخلية - التغير المناخي وسوء الإدارة",
        "6. التأثير الزراعي - تراجع الإنتاج الزراعي",
        "7. التأثيرات الاجتماعية - النزوح والبطالة",
        "8. تأثير الكهرباء - انخفاض الإنتاج الكهرومائي",
        "9. الحلول المقترحة - التفاوض والإدارة المحسنة",
        "10. الحلول التقنية - تحلية المياه والتقنيات الحديثة",
        "11. دور المجتمع الدولي - الضغط والمساعدات",
        "12. الخاتمة - إنقاذ بلاد الرافدين"
    ]
    
    for topic in topics:
        print(f"   {topic}")
    
    print("\n📈 إحصائيات مهمة / Key Statistics:")
    print("-" * 80)
    print("• انخفاض منسوب المياه: 29% في دجلة، 73% في الفرات")
    print("• عدد المتضررين: 7 ملايين عراقي")
    print("• النازحون: أكثر من 400 ألف شخص")
    print("• تراجع المساحات المزروعة: 70%")
    print("• عدد السدود التركية: 22 سداً")
    print("• التهديد بالجفاف الكامل: بحلول عام 2040")
    
    print("\n🛠️ كيفية التشغيل / How to Run:")
    print("-" * 80)
    print("1. تشغيل السكريبت الرئيسي:")
    print("   python main_tigris_euphrates_project.py")
    print()
    print("2. أو تشغيل كل سكريبت منفصلاً:")
    print("   python tigris_euphrates_drought_presentation.py")
    print("   python pptx_to_video_converter.py")
    
    print("\n💻 المتطلبات التقنية / Technical Requirements:")
    print("-" * 80)
    print("pip install python-pptx Pillow moviepy numpy")
    
    print("\n🎨 الألوان المستخدمة / Color Scheme:")
    print("-" * 80)
    print("• الأزرق الداكن (0, 51, 102) - للعناوين الرئيسية")
    print("• الأحمر الداكن (139, 0, 0) - للمشاكل والتحديات")
    print("• الأخضر (34, 139, 34) - للحلول والمقترحات")
    print("• الذهبي (218, 165, 32) - للخاتمة والتأكيدات")
    
    print("\n" + "=" * 80)
    print("تم إكمال المشروع بنجاح! ✅")
    print("Project completed successfully! ✅")
    print("=" * 80)

if __name__ == "__main__":
    display_project_summary()
