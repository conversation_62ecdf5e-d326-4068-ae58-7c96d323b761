#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشروع شامل: إنشاء عرض تقديمي وفيديو حول جفاف مياه دجلة والفرات في العراق
Complete Project: Create PowerPoint presentation and video about Tigris and Euphrates drought in Iraq
"""

import os
import sys
import subprocess

def install_required_packages():
    """تثبيت المكتبات المطلوبة"""
    required_packages = [
        'python-pptx',
        'Pillow',
        'moviepy',
        'numpy'
    ]
    
    print("تثبيت المكتبات المطلوبة...")
    
    for package in required_packages:
        try:
            print(f"تثبيت {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError:
            print(f"فشل في تثبيت {package}")
            return False
    
    print("تم تثبيت جميع المكتبات بنجاح!")
    return True

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("مشروع جفاف مياه دجلة والفرات في العراق")
    print("Tigris and Euphrates Drought in Iraq Project")
    print("=" * 60)
    
    # تثبيت المكتبات المطلوبة
    if not install_required_packages():
        print("فشل في تثبيت المكتبات المطلوبة")
        return
    
    try:
        # استيراد الوحدات بعد التثبيت
        from tigris_euphrates_drought_presentation import create_tigris_euphrates_presentation
        from pptx_to_video_converter import convert_presentation_to_video
        
        print("\n" + "=" * 40)
        print("الخطوة 1: إنشاء العرض التقديمي")
        print("=" * 40)
        
        # إنشاء العرض التقديمي
        pptx_file = create_tigris_euphrates_presentation()
        
        if os.path.exists(pptx_file):
            print(f"✓ تم إنشاء العرض التقديمي بنجاح: {pptx_file}")
            
            print("\n" + "=" * 40)
            print("الخطوة 2: تحويل العرض إلى فيديو")
            print("=" * 40)
            
            # تحويل العرض التقديمي إلى فيديو
            video_file = "جفاف_دجلة_والفرات_فيديو.mp4"
            success = convert_presentation_to_video(pptx_file, video_file, duration_per_slide=5)
            
            if success:
                print(f"✓ تم إنشاء الفيديو بنجاح: {video_file}")
                print("\n" + "=" * 50)
                print("تم إكمال المشروع بنجاح!")
                print("=" * 50)
                print(f"العرض التقديمي: {pptx_file}")
                print(f"الفيديو: {video_file}")
                print("مدة كل شريحة في الفيديو: 5 ثوانٍ")
                print("=" * 50)
            else:
                print("✗ فشل في إنشاء الفيديو")
        else:
            print("✗ فشل في إنشاء العرض التقديمي")
            
    except ImportError as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المكتبات المطلوبة")
    except Exception as e:
        print(f"خطأ عام: {e}")

if __name__ == "__main__":
    main()
