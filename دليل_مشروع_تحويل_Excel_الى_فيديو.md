# دليل مشروع تحويل Excel إلى فيديو

## 📋 ملخص المشروع

تم بنجاح تحويل ملف Excel "العلاقات_الكويتية_العراقية_محسن_نهائي_20250711.xlsx" إلى مجموعة من الإطارات المرئية بواقع 5 ثواني لكل إطار.

## 📊 معلومات الملف الأصلي

- **اسم الملف**: العلاقات_الكويتية_العراقية_محسن_نهائي_20250711.xlsx
- **عدد الصفوف**: 20 صف
- **عدد الأعمدة**: 7 أعمدة
- **المحتوى**: تطور العلاقات الكويتية العراقية عبر التاريخ - دراسة تاريخية شاملة ومفصلة

## 🎬 النتائج المُنشأة

### الإطارات المُنشأة
- **عدد الإطارات**: 21 إطار (1 إطار عنوان + 20 إطار بيانات)
- **مجلد الإطارات**: `excel_video_frames`
- **دقة الإطارات**: 1920x1080 (Full HD)
- **مدة كل إطار**: 5 ثواني
- **المدة الإجمالية للفيديو**: 105 ثانية (1 دقيقة و 45 ثانية)

### قائمة الإطارات
```
frame_000_title.png     - إطار العنوان الرئيسي
frame_001_data.png      - البيانات الصف 1
frame_002_data.png      - البيانات الصف 2
...
frame_020_data.png      - البيانات الصف 20
```

## 🛠️ الملفات المُنشأة

### 1. ملفات Python
- `simple_excel_to_video.py` - المحول الرئيسي
- `excel_to_video_converter.py` - المحول المتقدم
- `create_video_from_frames.py` - محول الإطارات إلى فيديو
- `run_excel_to_video.py` - سكريبت التشغيل السريع

### 2. ملفات المتطلبات
- `requirements_excel_video.txt` - قائمة المكتبات المطلوبة

### 3. ملفات التعليمات
- `تعليمات_إنشاء_الفيديو.txt` - تعليمات مفصلة لإنشاء الفيديو
- `create_video.bat` - سكريبت FFmpeg (إذا كان متوفر)

## 🎯 طرق إنشاء الفيديو النهائي

### الطريقة الأولى: برامج تحرير الفيديو المجانية

#### 1. DaVinci Resolve (الأفضل - مجاني)
- حمل من: https://www.blackmagicdesign.com/products/davinciresolve
- أنشئ مشروع جديد بدقة 1920x1080 و 30 FPS
- استورد جميع الصور من مجلد `excel_video_frames`
- اسحب الصور للتايم لاين بالترتيب
- اضبط مدة كل صورة على 5 ثواني
- صدر كـ MP4

#### 2. OpenShot (سهل الاستخدام - مجاني)
- حمل من: https://www.openshot.org
- أنشئ مشروع جديد
- استورد الصور
- اسحبها للتايم لاين
- انقر بالزر الأيمن على كل صورة واختر Properties
- غير Duration إلى 5.00 ثواني
- صدر الفيديو

#### 3. Shotcut (متقدم - مجاني)
- حمل من: https://shotcut.org
- أنشئ مشروع جديد
- اضبط Video Mode على HD 1080p 30 fps
- استورد الصور
- اسحبها للتايم لاين
- اضبط مدة كل صورة على 5 ثواني

### الطريقة الثانية: Microsoft PowerPoint

1. افتح PowerPoint
2. أنشئ عرض تقديمي جديد
3. استورد كل صورة في شريحة منفصلة
4. اذهب إلى Slide Show > Set Up Slide Show
5. اختر "Browsed at a kiosk" واضبط على 5 ثواني
6. صدر كفيديو: File > Export > Create a Video

### الطريقة الثالثة: مواقع الإنترنت

#### 1. Canva (canva.com)
- أنشئ حساب مجاني
- اختر Video template
- استورد الصور
- اضبط مدة كل صورة على 5 ثواني
- حمل الفيديو

#### 2. Kapwing (kapwing.com)
- ارفع الصور
- أنشئ slideshow
- اضبط التوقيت
- حمل النتيجة

## 🔧 المواصفات التقنية

### إعدادات الفيديو المقترحة
- **الدقة**: 1920x1080 (Full HD)
- **معدل الإطارات**: 30 FPS
- **الصيغة**: MP4
- **الترميز**: H.264
- **جودة الصوت**: لا يوجد صوت (فيديو صامت)

### خصائص الإطارات
- **الخلفية**: أبيض
- **لون العنوان**: أزرق داكن (#003366)
- **لون النص**: رمادي داكن (#333333)
- **الخط**: Tahoma (مع دعم العربية)
- **اتجاه النص**: من اليمين إلى اليسار (RTL)

## 📁 هيكل المجلدات

```
المجلد الرئيسي/
├── العلاقات_الكويتية_العراقية_محسن_نهائي_20250711.xlsx
├── excel_video_frames/
│   ├── frame_000_title.png
│   ├── frame_001_data.png
│   ├── frame_002_data.png
│   └── ... (21 إطار إجمالي)
├── simple_excel_to_video.py
├── excel_to_video_converter.py
├── create_video_from_frames.py
├── run_excel_to_video.py
├── requirements_excel_video.txt
├── تعليمات_إنشاء_الفيديو.txt
├── create_video.bat
└── دليل_مشروع_تحويل_Excel_الى_فيديو.md
```

## 🚀 كيفية التشغيل

### للمرة الأولى
```bash
# تثبيت المتطلبات
pip install -r requirements_excel_video.txt

# تشغيل المحول
python simple_excel_to_video.py
```

### التشغيل السريع
```bash
python run_excel_to_video.py
```

## 🎨 التخصيص

يمكن تخصيص الإعدادات التالية في الكود:

### الألوان
```python
self.bg_color = (255, 255, 255)      # لون الخلفية
self.title_color = (0, 51, 102)      # لون العنوان
self.text_color = (51, 51, 51)       # لون النص
self.header_color = (0, 102, 204)    # لون الرأس
```

### الأبعاد والتوقيت
```python
self.width = 1920                    # عرض الإطار
self.height = 1080                   # ارتفاع الإطار
self.frame_duration = 5              # مدة كل إطار بالثواني
self.margin = 80                     # الهامش
self.line_spacing = 50               # المسافة بين الأسطر
```

### الخطوط
```python
font_paths = [
    "C:/Windows/Fonts/tahoma.ttf",
    "C:/Windows/Fonts/arial.ttf",
    "fonts/Amiri-Regular.ttf",
    "fonts/Scheherazade-Regular.ttf"
]
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في قراءة ملف Excel
```
الحل: تأكد من وجود الملف في نفس المجلد ومن صحة اسم الملف
```

#### 2. مشاكل في الخطوط العربية
```
الحل: تأكد من تثبيت مكتبات arabic-reshaper و python-bidi
pip install arabic-reshaper python-bidi
```

#### 3. مشاكل في إنشاء الفيديو
```
الحل: استخدم الطرق اليدوية المذكورة أعلاه أو ثبت FFmpeg
```

## 📞 الدعم والمساعدة

في حالة مواجهة أي مشاكل:

1. تأكد من تثبيت جميع المتطلبات
2. راجع ملف التعليمات المفصل
3. استخدم الطرق اليدوية لإنشاء الفيديو
4. تحقق من صحة ملف Excel الأصلي

## 🎉 النتيجة النهائية

تم بنجاح تحويل ملف Excel إلى 21 إطار مرئي عالي الجودة، جاهز لتحويله إلى فيديو بمدة إجمالية 105 ثانية (1 دقيقة و 45 ثانية) بواقع 5 ثواني لكل إطار.

---

**تاريخ الإنشاء**: 14 يوليو 2025  
**الإصدار**: 1.0  
**المطور**: Augment Agent
