# 🤖 مولد فيديوهات المحادثة بالذكاء الاصطناعي

مولد فيديوهات احترافي لإنشاء محادثات واقعية بأسلوب الذكاء الاصطناعي المنتشر على وسائل التواصل الاجتماعي.

## ✨ المميزات

### 🎬 إنتاج فيديو احترافي
- **دقة عالية**: 1080x1920 (9:16) مثالي لـ YouTube Shorts
- **مدة مناسبة**: أقل من 60 ثانية للمنصات الاجتماعية
- **جودة عالية**: H.264 مع صوت AAC

### 🎭 شخصيات متنوعة
- **صوتين مختلفين**: أحمد وسارة بخصائص صوتية مميزة
- **حوار واقعي**: محادثات يومية وساخرة
- **مؤشرات الشخصيات**: عرض اسم المتحدث

### 🎨 مؤثرات بصرية متقدمة
- **خلفية متحركة**: تدرجات لونية ديناميكية
- **جسيمات متحركة**: تأثيرات بصرية جذابة
- **نصوص متحركة**: تأثير الكتابة التدريجية
- **انتقالات سلسة**: تأثيرات fade وcrossfade

### 🎵 صوت متقدم
- **تحويل النص لكلام**: باستخدام pyttsx3
- **أصوات مخصصة**: معدلات وأحجام مختلفة لكل شخصية
- **مزامنة مثالية**: بين الصوت والنص

## 🚀 طريقة الاستخدام

### الطريقة السريعة
```bash
python run_ai_video.py
```

### الطريقة اليدوية
1. **تثبيت المتطلبات**:
```bash
pip install -r requirements_ai_video.txt
```

2. **تشغيل المولد**:
```bash
python ai_conversation_video.py
```

## 📋 المتطلبات التقنية

### المكتبات المطلوبة
- `moviepy==1.0.3` - تحرير الفيديو
- `Pillow==10.0.0` - معالجة الصور
- `pyttsx3==2.90` - تحويل النص لكلام
- `numpy==1.24.3` - العمليات الرياضية
- `arabic-reshaper==3.0.0` - دعم النصوص العربية
- `python-bidi==0.4.2` - دعم الكتابة من اليمين لليسار

### متطلبات النظام
- Python 3.7 أو أحدث
- 4GB RAM (8GB مفضل)
- 500MB مساحة فارغة
- Windows/macOS/Linux

## 🎯 المحتوى المُنتج

### موضوع المحادثة
محادثة ساخرة بين أحمد وسارة حول:
- الذكاء الاصطناعي والتوقعات المبالغ فيها
- الفجوة بين الوعود والواقع
- مواقف يومية مضحكة مع التكنولوجيا

### مدة الفيديو
- **المحادثة**: ~32 ثانية
- **العنوان**: 4 ثواني
- **الخاتمة**: 3 ثواني
- **المجموع**: 55 ثانية (مثالي للمنصات الاجتماعية)

## 📱 منصات النشر المدعومة

### مُحسن للمنصات التالية:
- **YouTube Shorts** (9:16, أقل من 60 ثانية)
- **TikTok** (عمودي، محتوى قصير)
- **Instagram Reels** (1080x1920)
- **Facebook Stories** (عمودي)
- **WhatsApp Status** (فيديو قصير)

## 🎨 التخصيص

### تعديل المحادثة
يمكنك تعديل المحادثة في ملف `ai_conversation_video.py`:

```python
self.conversation = [
    {
        'speaker': 'person1',
        'text': 'النص الجديد هنا',
        'duration': 3.5
    },
    # أضف المزيد من الحوارات...
]
```

### تعديل الشخصيات
```python
self.characters = {
    'person1': {
        'name': 'الاسم الجديد',
        'voice_rate': 180,  # سرعة الكلام
        'voice_volume': 0.9,  # مستوى الصوت
        'color': (100, 200, 255),  # لون النص
        'position': 'left'  # موقع النص
    }
}
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**مشكلة**: خطأ في تثبيت moviepy
```bash
# الحل
pip install --upgrade pip
pip install moviepy --no-cache-dir
```

**مشكلة**: لا يعمل الصوت
```bash
# تأكد من تثبيت pyttsx3
pip install --upgrade pyttsx3
```

**مشكلة**: خطأ في الخطوط
- الحل: سيتم استخدام الخط الافتراضي تلقائياً

## 📊 مواصفات الإخراج

### جودة الفيديو
- **الدقة**: 1080x1920 بكسل
- **معدل الإطارات**: 30 FPS
- **الترميز**: H.264
- **جودة**: CRF 23 (مناسب للمنصات الاجتماعية)

### جودة الصوت
- **الترميز**: AAC
- **معدل البت**: تلقائي
- **القنوات**: Mono/Stereo

## 🎉 النتيجة النهائية

سيتم إنشاء فيديو بالمواصفات التالية:
- ✅ محادثة واقعية وساخرة
- ✅ صوتين مختلفين ومميزين
- ✅ خلفية متحركة جذابة
- ✅ نصوص واضحة مع تأثيرات
- ✅ انتقالات سلسة ومهنية
- ✅ مُحسن للمنصات الاجتماعية
- ✅ جاهز للنشر فوراً

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من إصدار Python (3.7+)
3. جرب إعادة تثبيت المكتبات
4. تأكد من وجود مساحة كافية على القرص

---

**استمتع بإنشاء محتوى ترفيهي احترافي! 🎬✨**
