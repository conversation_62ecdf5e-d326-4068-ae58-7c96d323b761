#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick runner for Excel to Video conversion
تشغيل سريع لتحويل Excel إلى فيديو
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_excel_video.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def run_converter():
    """Run the Excel to Video converter"""
    print("Running Excel to Video converter...")
    try:
        from excel_to_video_converter import main
        main()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all requirements are installed.")
        return False
    except Exception as e:
        print(f"❌ Error running converter: {e}")
        return False
    
    return True

def main():
    print("🎬 Excel to Video Converter")
    print("=" * 50)
    
    # Check if requirements file exists
    if not os.path.exists("requirements_excel_video.txt"):
        print("❌ Requirements file not found!")
        return
    
    # Install requirements
    if not install_requirements():
        return
    
    print("\n" + "=" * 50)
    
    # Run converter
    run_converter()

if __name__ == "__main__":
    main()
