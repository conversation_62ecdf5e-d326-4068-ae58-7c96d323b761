#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Excel to Video Converter
تحويل ملف Excel إلى فيديو بسيط بواقع 5 ثواني لكل فريم
"""

import os
import sys
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime

class SimpleExcelToVideoConverter:
    def __init__(self, excel_file):
        self.excel_file = excel_file
        self.width = 1920
        self.height = 1080
        self.frame_duration = 5  # 5 seconds per frame
        self.margin = 80
        self.line_spacing = 50
        
        # Colors
        self.bg_color = (255, 255, 255)  # White background
        self.title_color = (0, 51, 102)  # Dark blue
        self.text_color = (51, 51, 51)   # Dark gray
        self.header_color = (0, 102, 204)  # Blue
        
        # Create output directory
        self.output_dir = "excel_video_frames"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def load_arabic_font(self, size=24):
        """Load Arabic font with fallback options"""
        font_paths = [
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/arial.ttf",
            "fonts/Amiri-Regular.ttf",
            "fonts/Scheherazade-Regular.ttf"
        ]
        
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, size)
            except Exception as e:
                continue
        
        # Fallback to default font
        return ImageFont.load_default()
    
    def format_arabic_text(self, text):
        """Format Arabic text for proper RTL display"""
        try:
            if isinstance(text, (int, float)):
                return str(text)
            
            text = str(text)
            # Use arabic_reshaper with default settings
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm for proper RTL display
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception as e:
            print(f"Warning: Arabic text processing failed: {e}")
            return str(text)
    
    def read_excel_data(self):
        """Read Excel file and return data"""
        try:
            # Try to read Excel file
            df = pd.read_excel(self.excel_file)
            print(f"Excel file loaded successfully with {len(df)} rows and {len(df.columns)} columns")
            print(f"Columns: {list(df.columns)}")
            return df
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return None
    
    def create_title_frame(self, title_text):
        """Create title frame"""
        img = Image.new('RGB', (self.width, self.height), self.bg_color)
        draw = ImageDraw.Draw(img)
        
        # Load fonts
        title_font = self.load_arabic_font(48)
        subtitle_font = self.load_arabic_font(32)
        
        # Format title text
        formatted_title = self.format_arabic_text(title_text)
        
        # Draw background rectangle for title
        title_rect_height = 200
        draw.rectangle([0, self.height//2 - title_rect_height//2, 
                       self.width, self.height//2 + title_rect_height//2], 
                      fill=self.title_color)
        
        # Draw title
        title_bbox = draw.textbbox((0, 0), formatted_title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = self.height // 2 - 30
        
        draw.text((title_x, title_y), formatted_title, 
                 font=title_font, fill=(255, 255, 255))
        
        # Add subtitle
        subtitle = "تحليل البيانات والعلاقات"
        formatted_subtitle = self.format_arabic_text(subtitle)
        subtitle_bbox = draw.textbbox((0, 0), formatted_subtitle, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = title_y + 60
        
        draw.text((subtitle_x, subtitle_y), formatted_subtitle, 
                 font=subtitle_font, fill=(255, 255, 255))
        
        return img
    
    def create_data_frame(self, row_data, row_index, total_rows):
        """Create frame for data row"""
        img = Image.new('RGB', (self.width, self.height), self.bg_color)
        draw = ImageDraw.Draw(img)
        
        # Load fonts
        header_font = self.load_arabic_font(36)
        data_font = self.load_arabic_font(28)
        small_font = self.load_arabic_font(20)
        
        # Draw header
        header_text = f"البيانات - الصف {row_index + 1} من {total_rows}"
        formatted_header = self.format_arabic_text(header_text)
        
        # Header background
        draw.rectangle([0, 0, self.width, 100], fill=self.header_color)
        
        # Header text
        header_bbox = draw.textbbox((0, 0), formatted_header, font=header_font)
        header_width = header_bbox[2] - header_bbox[0]
        header_x = (self.width - header_width) // 2
        draw.text((header_x, 30), formatted_header, font=header_font, fill=(255, 255, 255))
        
        # Draw data
        y_pos = 150
        
        for column, value in row_data.items():
            if pd.isna(value):
                continue
                
            # Format column name and value
            formatted_column = self.format_arabic_text(str(column))
            formatted_value = self.format_arabic_text(str(value))
            
            # Draw column name
            draw.text((self.margin, y_pos), f"{formatted_column}:", 
                     font=data_font, fill=self.title_color)
            
            # Draw value (with text wrapping if needed)
            value_lines = self.wrap_text(formatted_value, data_font, self.width - 2 * self.margin - 200)
            value_y = y_pos
            
            for line in value_lines:
                draw.text((self.margin + 200, value_y), line, 
                         font=data_font, fill=self.text_color)
                value_y += self.line_spacing
            
            y_pos = max(y_pos + self.line_spacing + 20, value_y + 20)
            
            # Check if we need to break to avoid overflow
            if y_pos > self.height - 150:
                break
        
        # Draw footer
        footer_text = f"الصفحة {row_index + 1} من {total_rows}"
        formatted_footer = self.format_arabic_text(footer_text)
        draw.text((self.width // 2 - 100, self.height - 50), formatted_footer, 
                 font=small_font, fill=self.text_color)
        
        return img
    
    def wrap_text(self, text, font, max_width):
        """Wrap text to fit within max_width"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = f"{current_line} {word}".strip()
            bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def create_frames(self):
        """Create all frames from Excel data"""
        # Read Excel data
        df = self.read_excel_data()
        if df is None:
            return []
        
        frames = []
        
        # Create title frame
        title_text = "العلاقات الكويتية العراقية"
        title_img = self.create_title_frame(title_text)
        title_path = os.path.join(self.output_dir, "frame_000_title.png")
        title_img.save(title_path)
        frames.append(title_path)
        print(f"Created title frame: {title_path}")
        
        # Create frames for each row
        for index, row in df.iterrows():
            frame_img = self.create_data_frame(row, index, len(df))
            frame_path = os.path.join(self.output_dir, f"frame_{index+1:03d}_data.png")
            frame_img.save(frame_path)
            frames.append(frame_path)
            print(f"Created frame {index+1}/{len(df)}: {frame_path}")
        
        return frames
    
    def create_video_instructions(self, frames):
        """Create instructions for manual video creation"""
        if not frames:
            print("No frames to create video!")
            return None
        
        print(f"\n🎬 تم إنشاء {len(frames)} إطار بنجاح!")
        print(f"📁 مجلد الإطارات: {self.output_dir}")
        print(f"⏱️ مدة كل إطار: {self.frame_duration} ثواني")
        print(f"⏱️ المدة الإجمالية: {len(frames) * self.frame_duration} ثانية")
        
        print("\n📋 تعليمات إنشاء الفيديو:")
        print("1. يمكنك استخدام أي برنامج تحرير فيديو مثل:")
        print("   - Adobe Premiere Pro")
        print("   - DaVinci Resolve (مجاني)")
        print("   - OpenShot (مجاني)")
        print("   - Shotcut (مجاني)")
        
        print("\n2. خطوات إنشاء الفيديو:")
        print("   - استورد جميع الصور من مجلد excel_video_frames")
        print("   - رتب الصور حسب الترقيم")
        print("   - اضبط مدة كل صورة على 5 ثواني")
        print("   - اختر دقة 1920x1080 (Full HD)")
        print("   - اختر معدل إطارات 30 FPS")
        print("   - صدر الفيديو بصيغة MP4")
        
        # Create a simple batch file for ffmpeg if available
        self.create_ffmpeg_script(frames)
        
        return frames
    
    def create_ffmpeg_script(self, frames):
        """Create FFmpeg script for video creation"""
        script_content = f"""@echo off
echo Creating video from frames...

REM Check if ffmpeg is available
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo FFmpeg not found. Please install FFmpeg first.
    echo Download from: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM Create video from frames
ffmpeg -framerate 1/{self.frame_duration} -pattern_type glob -i "{self.output_dir}\\*.png" -c:v libx264 -pix_fmt yuv420p -r 30 "العلاقات_الكويتية_العراقية_فيديو_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"

echo Video created successfully!
pause
"""
        
        with open("create_video.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print(f"\n🎥 تم إنشاء ملف create_video.bat")
        print("   - قم بتشغيل هذا الملف إذا كان لديك FFmpeg مثبت")
        print("   - أو استخدم برنامج تحرير فيديو كما هو موضح أعلاه")
    
    def convert(self):
        """Main conversion function"""
        print(f"Starting conversion of {self.excel_file} to video frames...")
        print(f"Frame duration: {self.frame_duration} seconds")
        
        # Create frames
        frames = self.create_frames()
        
        if not frames:
            print("Failed to create frames!")
            return None
        
        # Create instructions
        self.create_video_instructions(frames)
        
        return frames

def main():
    # Excel file to convert
    excel_file = "العلاقات_الكويتية_العراقية_محسن_نهائي_20250711.xlsx"
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"Error: Excel file '{excel_file}' not found!")
        print("Available files:")
        for file in os.listdir('.'):
            if file.endswith('.xlsx'):
                print(f"  - {file}")
        return
    
    # Create converter and convert
    converter = SimpleExcelToVideoConverter(excel_file)
    frames = converter.convert()
    
    if frames:
        print(f"\n🎉 تم إنشاء الإطارات بنجاح!")
        print(f"📁 عدد الإطارات: {len(frames)}")

if __name__ == "__main__":
    main()
