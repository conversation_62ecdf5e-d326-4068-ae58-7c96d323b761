#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel للدول الداعمة لإسرائيل مع تمييز الدول العربية باللون الأحمر
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

def create_israel_supporters_excel():
    """إنشاء ملف Excel للدول الداعمة لإسرائيل"""
    
    # إنشاء مصنف جديد
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "الدول الداعمة لإسرائيل"
    
    # تعيين اتجاه النص من اليمين لليسار
    ws.sheet_view.rightToLeft = True
    
    # تعريف الخطوط والألوان
    arabic_font = Font(name='Arabic Typesetting', size=14, bold=True)
    header_font = Font(name='Arabic Typesetting', size=16, bold=True, color='FFFFFF')
    arab_fill = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')  # أحمر للدول العربية
    header_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')  # رمادي داكن للعنوان
    normal_fill = PatternFill(start_color='F0F8FF', end_color='F0F8FF', fill_type='solid')  # أزرق فاتح للدول الأخرى
    
    # تعريف الحدود
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # تعريف المحاذاة
    center_alignment = Alignment(horizontal='center', vertical='center', text_rotation=0)
    
    # إعداد العناوين
    headers = ['الرقم', 'اسم الدولة', 'نوع الدعم', 'ملاحظات']
    
    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border
    
    # بيانات الدول الداعمة لإسرائيل
    countries_data = [
        # الدول العربية المطبعة (ستظهر باللون الأحمر)
        (2, 'الإمارات العربية المتحدة', 'تطبيع كامل - اتفاقيات أبراهام', 'دولة عربية مطبعة', True),
        (3, 'البحرين', 'تطبيع كامل - اتفاقيات أبراهام', 'دولة عربية مطبعة', True),
        (4, 'المغرب', 'تطبيع كامل - اتفاقيات أبراهام', 'دولة عربية مطبعة', True),
        (5, 'السودان', 'تطبيع جزئي - اتفاقيات أبراهام', 'دولة عربية مطبعة (معلق)', True),
        (6, 'مصر', 'معاهدة سلام - كامب ديفيد', 'دولة عربية مطبعة منذ 1979', True),
        (7, 'الأردن', 'معاهدة سلام وادي عربة', 'دولة عربية مطبعة منذ 1994', True),
        
        # الدول الغربية والحلفاء الرئيسيين
        (8, 'الولايات المتحدة الأمريكية', 'دعم عسكري ومالي وسياسي كامل', 'أكبر داعم لإسرائيل', False),
        (9, 'ألمانيا', 'دعم عسكري ومالي وسياسي', 'دعم قوي تاريخي', False),
        (10, 'المملكة المتحدة', 'دعم سياسي ودبلوماسي', 'حليف تاريخي', False),
        (11, 'فرنسا', 'دعم سياسي ودبلوماسي', 'علاقات قوية', False),
        (12, 'كندا', 'دعم سياسي ودبلوماسي', 'حليف قوي', False),
        (13, 'أستراليا', 'دعم سياسي ودبلوماسي', 'حليف في المحيط الهادئ', False),
        (14, 'هولندا', 'دعم سياسي وتجاري', 'علاقات قوية', False),
        (15, 'إيطاليا', 'دعم سياسي وتجاري', 'علاقات جيدة', False),
        (16, 'إسبانيا', 'دعم سياسي محدود', 'علاقات دبلوماسية', False),
        (17, 'بولندا', 'دعم سياسي ودبلوماسي', 'علاقات متنامية', False),
        (18, 'التشيك', 'دعم سياسي قوي', 'حليف في أوروبا الشرقية', False),
        (19, 'المجر', 'دعم سياسي', 'علاقات جيدة', False),
        (20, 'رومانيا', 'دعم سياسي ودبلوماسي', 'علاقات تاريخية', False),
        
        # دول أمريكا اللاتينية
        (21, 'البرازيل', 'علاقات دبلوماسية', 'علاقات متقلبة', False),
        (22, 'الأرجنتين', 'علاقات دبلوماسية', 'علاقات تاريخية', False),
        (23, 'كولومبيا', 'دعم سياسي', 'علاقات جيدة', False),
        (24, 'غواتيمالا', 'دعم سياسي قوي', 'نقل السفارة للقدس', False),
        (25, 'هندوراس', 'دعم سياسي', 'علاقات جيدة', False),
        
        # دول آسيوية وأفريقية
        (26, 'الهند', 'علاقات استراتيجية متنامية', 'شراكة تكنولوجية وعسكرية', False),
        (27, 'اليابان', 'علاقات اقتصادية وتكنولوجية', 'شراكة تجارية', False),
        (28, 'كوريا الجنوبية', 'علاقات تكنولوجية', 'تعاون تقني', False),
        (29, 'سنغافورة', 'علاقات تجارية', 'مركز مالي', False),
        (30, 'الفلبين', 'علاقات دبلوماسية', 'تعاون محدود', False),
        (31, 'كينيا', 'علاقات دبلوماسية', 'تعاون تنموي', False),
        (32, 'غانا', 'علاقات دبلوماسية', 'تعاون اقتصادي', False),
        (33, 'نيجيريا', 'علاقات تجارية', 'تعاون محدود', False),
        
        # دول أوروبية أخرى
        (34, 'النمسا', 'علاقات دبلوماسية', 'علاقات جيدة', False),
        (35, 'سويسرا', 'علاقات دبلوماسية', 'وساطة أحياناً', False),
        (36, 'الدنمارك', 'علاقات دبلوماسية', 'علاقات جيدة', False),
        (37, 'السويد', 'علاقات دبلوماسية', 'علاقات متقلبة', False),
        (38, 'النرويج', 'علاقات دبلوماسية', 'وساطة تاريخية', False),
        (39, 'فنلندا', 'علاقات دبلوماسية', 'علاقات محايدة', False),
        (40, 'بلجيكا', 'علاقات دبلوماسية', 'مقر الاتحاد الأوروبي', False),
    ]
    
    # كتابة البيانات
    for row_data in countries_data:
        row_num, country, support_type, notes, is_arab = row_data
        
        # كتابة البيانات في الخلايا
        ws.cell(row=row_num, column=1, value=row_num-1)  # الرقم
        ws.cell(row=row_num, column=2, value=country)    # اسم الدولة
        ws.cell(row=row_num, column=3, value=support_type)  # نوع الدعم
        ws.cell(row=row_num, column=4, value=notes)      # ملاحظات
        
        # تطبيق التنسيق
        for col in range(1, 5):
            cell = ws.cell(row=row_num, column=col)
            cell.font = arabic_font
            cell.alignment = center_alignment
            cell.border = border
            
            # تلوين الدول العربية باللون الأحمر
            if is_arab:
                cell.fill = arab_fill
                cell.font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
            else:
                cell.fill = normal_fill
    
    # تعديل عرض الأعمدة
    column_widths = [8, 25, 35, 30]  # عرض كل عمود
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width
    
    # إضافة عنوان رئيسي
    ws.insert_rows(1)
    ws.merge_cells('A1:D1')
    title_cell = ws['A1']
    title_cell.value = 'الدول الداعمة لدولة إسرائيل - الدول العربية مميزة باللون الأحمر'
    title_cell.font = Font(name='Arabic Typesetting', size=18, bold=True, color='FFFFFF')
    title_cell.fill = PatternFill(start_color='8B0000', end_color='8B0000', fill_type='solid')
    title_cell.alignment = center_alignment
    title_cell.border = border
    
    # تعديل ارتفاع الصف الأول
    ws.row_dimensions[1].height = 30
    
    # حفظ الملف
    filename = 'الدول_الداعمة_لإسرائيل.xlsx'
    wb.save(filename)
    print(f"تم إنشاء الملف بنجاح: {filename}")
    print(f"الملف يحتوي على {len(countries_data)} دولة")
    print("الدول العربية المطبعة مميزة باللون الأحمر")
    
    return filename

if __name__ == "__main__":
    create_israel_supporters_excel()
