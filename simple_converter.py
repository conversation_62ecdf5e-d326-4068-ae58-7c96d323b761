import os
from PIL import Image, ImageDraw, ImageFont
from docx import Document

def convert_docx_to_images():
    print("Converting UAE Travel Guide to Images...")
    
    # Settings
    width = 1080
    height = 1920
    margin = 80
    line_spacing = 40
    
    # Colors
    bg_color = (255, 255, 255)
    title_color = (0, 51, 102)
    text_color = (51, 51, 51)
    
    # Create output directory
    output_dir = "travel_guide_images"
    os.makedirs(output_dir, exist_ok=True)
    
    # Read document
    docx_file = "دليل_سفر_العراقيين_الى_الامارات_2024.docx"
    
    try:
        doc = Document(docx_file)
        print(f"Document loaded successfully")
    except Exception as e:
        print(f"Error loading document: {e}")
        return
    
    # Get all text
    all_text = []
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            all_text.append(text)
    
    print(f"Found {len(all_text)} paragraphs")
    
    # Split into pages
    pages = []
    current_page = []
    current_lines = 0
    max_lines_per_page = 25
    
    for text in all_text:
        estimated_lines = len(text) // 50 + 1
        
        if current_lines + estimated_lines > max_lines_per_page and current_page:
            pages.append(current_page)
            current_page = [text]
            current_lines = estimated_lines
        else:
            current_page.append(text)
            current_lines += estimated_lines
    
    if current_page:
        pages.append(current_page)
    
    print(f"Split into {len(pages)} pages")
    
    # Create images
    image_paths = []
    
    for page_num, page_content in enumerate(pages, 1):
        print(f"Creating page {page_num}...")
        
        # Create image
        img = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(img)
        
        # Try to load font
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/tahoma.ttf", 24)
            title_font = ImageFont.truetype("C:/Windows/Fonts/tahoma.ttf", 32)
        except:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
        
        # Draw header
        header_text = "UAE Travel Guide 2024"
        draw.rectangle([0, 0, width, 100], fill=title_color)
        draw.text((width//2 - 150, 30), header_text, font=title_font, fill=(255, 255, 255))
        
        # Draw content
        y_pos = 140
        
        for text in page_content:
            if y_pos > height - 150:
                break
                
            # Simple text wrapping
            words = text.split()
            lines = []
            current_line = ""
            
            for word in words:
                test_line = f"{current_line} {word}".strip()
                if len(test_line) < 60:  # Simple character limit
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word
            
            if current_line:
                lines.append(current_line)
            
            # Draw lines
            for line in lines:
                if y_pos < height - 100:
                    draw.text((margin, y_pos), line, font=font, fill=text_color)
                    y_pos += line_spacing
        
        # Draw footer
        footer_text = f"Page {page_num} of {len(pages)}"
        draw.text((width//2 - 50, height - 50), footer_text, font=font, fill=text_color)
        
        # Save image
        filename = f"travel_guide_page_{page_num:02d}.png"
        filepath = os.path.join(output_dir, filename)
        img.save(filepath, 'PNG')
        image_paths.append(filepath)
        
        print(f"Saved: {filename}")
    
    print(f"\nConversion complete!")
    print(f"Created {len(image_paths)} images in '{output_dir}' directory")
    
    # List files
    for i, path in enumerate(image_paths, 1):
        if os.path.exists(path):
            size_kb = os.path.getsize(path) / 1024
            filename = os.path.basename(path)
            print(f"  {i}. {filename} ({size_kb:.1f} KB)")

if __name__ == "__main__":
    convert_docx_to_images()
