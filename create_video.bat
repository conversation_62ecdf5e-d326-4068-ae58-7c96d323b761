@echo off
echo Creating video from frames...

REM Check if ffmpeg is available
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo FFmpeg not found. Please install FFmpeg first.
    echo Download from: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM Create video from frames
ffmpeg -framerate 1/5 -pattern_type glob -i "excel_video_frames\*.png" -c:v libx264 -pix_fmt yuv420p -r 30 "العلاقات_الكويتية_العراقية_فيديو_20250714_042325.mp4"

echo Video created successfully!
pause
