#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل دليل سفر العراقيين إلى الإمارات 2024 إلى صور
يقوم هذا البرنامج بتحويل ملف الورد إلى مجموعة من الصور عالية الجودة
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from docx import Document
import textwrap
import re

class WordToImageConverter:
    def __init__(self):
        self.width = 1080
        self.height = 1920  # 9:16 aspect ratio for social media
        self.margin = 80
        self.line_spacing = 40
        self.title_font_size = 48
        self.heading_font_size = 36
        self.body_font_size = 28
        self.small_font_size = 24

        # ألوان التصميم
        self.bg_color = (255, 255, 255)  # أبيض
        self.title_color = (0, 51, 102)  # أزرق داكن
        self.heading_color = (0, 102, 204)  # أزرق متوسط
        self.text_color = (51, 51, 51)  # رمادي داكن
        self.accent_color = (255, 193, 7)  # ذهبي

        # تحميل الخطوط العربية
        self.fonts = self.load_arabic_fonts()

    def load_arabic_fonts(self):
        """تحميل الخطوط العربية"""
        fonts = {}

        # مسارات الخطوط المحتملة
        font_paths = [
            "C:/Windows/Fonts/",
            "/System/Library/Fonts/",
            "/usr/share/fonts/",
            "./fonts/"
        ]

        # أسماء ملفات الخطوط
        font_files = {
            'amiri': ['Amiri-Regular.ttf', 'amiri-regular.ttf'],
            'scheherazade': ['Scheherazade-Regular.ttf', 'scheherazade-regular.ttf'],
            'arabic_typesetting': ['arabtype.ttf', 'Arabic Typesetting Regular.ttf']
        }

        # البحث عن الخطوط
        for font_name, filenames in font_files.items():
            for path in font_paths:
                for filename in filenames:
                    font_path = os.path.join(path, filename)
                    if os.path.exists(font_path):
                        try:
                            fonts[font_name] = font_path
                            print(f"✓ تم العثور على خط {font_name}: {font_path}")
                            break
                        except:
                            continue
                if font_name in fonts:
                    break

        # استخدام خط افتراضي إذا لم يتم العثور على خطوط عربية
        if not fonts:
            print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
            fonts['default'] = None

        return fonts

    def get_font(self, size, bold=False):
        """الحصول على خط بحجم محدد"""
        try:
            if 'amiri' in self.fonts:
                return ImageFont.truetype(self.fonts['amiri'], size)
            elif 'scheherazade' in self.fonts:
                return ImageFont.truetype(self.fonts['scheherazade'], size)
            elif 'arabic_typesetting' in self.fonts:
                return ImageFont.truetype(self.fonts['arabic_typesetting'], size)
            else:
                return ImageFont.load_default()
        except:
            return ImageFont.load_default()

    def wrap_arabic_text(self, text, font, max_width):
        """تقسيم النص العربي إلى أسطر"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            test_line = f"{current_line} {word}".strip()
            bbox = font.getbbox(test_line)
            text_width = bbox[2] - bbox[0]

            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines

    def draw_header(self, draw, y_pos):
        """رسم رأس الصفحة"""
        # رسم خلفية ملونة للرأس
        header_height = 120
        draw.rectangle([0, 0, self.width, header_height], fill=self.title_color)

        # عنوان رئيسي
        title = "دليل سفر العراقيين إلى الإمارات 2024"
        title_font = self.get_font(self.title_font_size, bold=True)

        # حساب موضع النص في المنتصف
        bbox = title_font.getbbox(title)
        text_width = bbox[2] - bbox[0]
        x_pos = (self.width - text_width) // 2

        draw.text((x_pos, 30), title, font=title_font, fill=(255, 255, 255), anchor="lt")

        return header_height + 40

    def draw_footer(self, draw, page_num, total_pages):
        """رسم تذييل الصفحة"""
        footer_y = self.height - 80

        # رقم الصفحة
        page_text = f"صفحة {page_num} من {total_pages}"
        footer_font = self.get_font(self.small_font_size)

        bbox = footer_font.getbbox(page_text)
        text_width = bbox[2] - bbox[0]
        x_pos = (self.width - text_width) // 2

        draw.text((x_pos, footer_y), page_text, font=footer_font, fill=self.text_color, anchor="lt")

        # خط فاصل
        draw.line([self.margin, footer_y - 20, self.width - self.margin, footer_y - 20],
                 fill=self.accent_color, width=2)

    def process_paragraph(self, paragraph):
        """معالجة فقرة من الوثيقة"""
        text = paragraph.text.strip()
        if not text:
            return None, None

        # تحديد نوع النص (عنوان، عنوان فرعي، نص عادي)
        if text.startswith('#'):
            return 'title', text.replace('#', '').strip()
        elif any(keyword in text for keyword in ['الورقة', 'الفصل', 'القسم']):
            return 'heading', text
        elif text.startswith('✅') or text.startswith('⚠️') or text.startswith('📋'):
            return 'bullet', text
        else:
            return 'body', text

    def draw_text_block(self, draw, text, text_type, y_pos, max_width):
        """رسم كتلة نص"""
        if text_type == 'title':
            font = self.get_font(self.heading_font_size, bold=True)
            color = self.title_color
        elif text_type == 'heading':
            font = self.get_font(self.heading_font_size)
            color = self.heading_color
        elif text_type == 'bullet':
            font = self.get_font(self.body_font_size)
            color = self.text_color
        else:
            font = self.get_font(self.body_font_size)
            color = self.text_color

        # تقسيم النص إلى أسطر
        lines = self.wrap_arabic_text(text, font, max_width)

        current_y = y_pos
        for line in lines:
            # محاذاة النص لليمين (للعربية)
            bbox = font.getbbox(line)
            text_width = bbox[2] - bbox[0]
            x_pos = self.width - self.margin - text_width

            draw.text((x_pos, current_y), line, font=font, fill=color, anchor="lt")
            current_y += self.line_spacing

        return current_y + 20  # مسافة إضافية بعد الفقرة

    def convert_document_to_images(self, docx_path, output_dir):
        """تحويل الوثيقة إلى صور"""
        print(f"🔄 بدء تحويل الملف: {docx_path}")

        # إنشاء مجلد الإخراج
        os.makedirs(output_dir, exist_ok=True)

        # قراءة الوثيقة
        try:
            doc = Document(docx_path)
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
            return []

        # معالجة الفقرات
        paragraphs = []
        for para in doc.paragraphs:
            result = self.process_paragraph(para)
            if result[0]:
                paragraphs.append(result)

        # تقسيم المحتوى إلى صفحات
        pages = self.split_content_to_pages(paragraphs)

        # إنشاء الصور
        image_paths = []
        total_pages = len(pages)

        for page_num, page_content in enumerate(pages, 1):
            print(f"📄 إنشاء الصفحة {page_num} من {total_pages}")

            # إنشاء صورة جديدة
            img = Image.new('RGB', (self.width, self.height), self.bg_color)
            draw = ImageDraw.Draw(img)

            # رسم الرأس
            current_y = self.draw_header(draw, 0)

            # رسم المحتوى
            max_width = self.width - (2 * self.margin)

            for text_type, text in page_content:
                if current_y < self.height - 150:  # ترك مساحة للتذييل
                    current_y = self.draw_text_block(draw, text, text_type, current_y, max_width)

            # رسم التذييل
            self.draw_footer(draw, page_num, total_pages)

            # حفظ الصورة
            filename = f"دليل_السفر_صفحة_{page_num:02d}.png"
            filepath = os.path.join(output_dir, filename)
            img.save(filepath, 'PNG', quality=95, optimize=True)
            image_paths.append(filepath)

            print(f"✅ تم حفظ: {filename}")

        return image_paths

    def split_content_to_pages(self, paragraphs):
        """تقسيم المحتوى إلى صفحات"""
        pages = []
        current_page = []
        current_height = 160  # ارتفاع الرأس
        max_height = self.height - 200  # ترك مساحة للتذييل

        for text_type, text in paragraphs:
            # تقدير ارتفاع النص
            if text_type == 'title':
                estimated_height = 80
            elif text_type == 'heading':
                estimated_height = 60
            else:
                # تقدير عدد الأسطر
                estimated_lines = len(text) // 50 + 1
                estimated_height = estimated_lines * self.line_spacing + 20

            # إذا كان النص لا يتسع في الصفحة الحالية
            if current_height + estimated_height > max_height and current_page:
                pages.append(current_page)
                current_page = [(text_type, text)]
                current_height = 160 + estimated_height
            else:
                current_page.append((text_type, text))
                current_height += estimated_height

        # إضافة الصفحة الأخيرة
        if current_page:
            pages.append(current_page)

        return pages

def main():
    """الدالة الرئيسية"""
    print("🎨 برنامج تحويل دليل السفر إلى صور")
    print("=" * 50)

    # مسار الملف
    docx_file = "دليل_سفر_العراقيين_الى_الامارات_2024.docx"
    output_directory = "travel_guide_images"  # استخدام اسم إنجليزي لتجنب مشاكل الترميز

    # التحقق من وجود الملف
    if not os.path.exists(docx_file):
        print(f"❌ الملف غير موجود: {docx_file}")
        return

    # إنشاء محول
    converter = WordToImageConverter()

    # تحويل الوثيقة
    try:
        image_paths = converter.convert_document_to_images(docx_file, output_directory)

        if image_paths:
            print(f"\n🎉 تم التحويل بنجاح!")
            print(f"📁 المجلد: {output_directory}")
            print(f"📊 عدد الصور: {len(image_paths)}")
            print(f"📐 الأبعاد: {converter.width}x{converter.height} (9:16)")

            # حساب الحجم الإجمالي
            total_size = 0
            for path in image_paths:
                if os.path.exists(path):
                    total_size += os.path.getsize(path)
            print(f"💾 الحجم الإجمالي: {total_size / 1024 / 1024:.1f} MB")

            print("\n📋 قائمة الصور المُنشأة:")
            for i, path in enumerate(image_paths, 1):
                filename = os.path.basename(path)
                if os.path.exists(path):
                    size_kb = os.path.getsize(path) / 1024
                    print(f"   {i:2d}. {filename} ({size_kb:.1f} KB)")
                else:
                    print(f"   {i:2d}. {filename} (ملف غير موجود)")

        else:
            print("❌ فشل في إنشاء الصور")

    except Exception as e:
        print(f"❌ خطأ في التحويل: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
